{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"jsx": "preserve", "incremental": true, "moduleResolution": "bundler", "module": "ESNext", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/cms/*": ["./src/cms/*"], "@repo/ui/*": ["../../packages/ui/src/*"], "@i18n/*": ["./src/i18n/*"], "@public/*": ["./public/*"]}, "allowJs": true, "noEmit": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next"]}