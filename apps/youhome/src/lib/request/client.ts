import { createCamelCaseApiClient, createSnakeCaseApiClient } from '@repo/api';
// import { isLocalRuntime } from '@repo/common/env/index';
import { UserWithPreferenceVO } from '@/schema/userSchema';

const isLocal = process.env.NODE_ENV === 'development';

export const apiClient = createCamelCaseApiClient({
  basePath: isLocal ? 'http://localhost:3001' : process.env.NEXT_PUBLIC_YOUMIND_API_BASE_PATH,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const apiClientOld = createSnakeCaseApiClient({
  basePath: isLocal ? 'http://localhost:3001' : '/',
  headers: {
    'Content-Type': 'application/json',
  },
});

export async function tryGetCurrentUserFromClient() {
  try {
    const user = await apiClient.userApi.getCurrentUser();
    return user as UserWithPreferenceVO;
  } catch (error: any) {
    return null;
  }
}
