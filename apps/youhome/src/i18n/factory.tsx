import { NextIntlClientProvider, useTranslations as useNextIntlTranslations } from 'next-intl';
import { ReactNode } from 'react';

// ============= 类型工具 =============

// 有限深度的嵌套键类型（最多5层）
type NestedKeyOf<T, D extends number = 5> = D extends 0
  ? never
  : T extends object
    ? {
        [K in keyof T]: K extends string
          ? T[K] extends object
            ? `${K}` | `${K}.${NestedKeyOf<T[K], Prev<D>>}`
            : `${K}`
          : never;
      }[keyof T]
    : never;

// 递减计数器类型
type Prev<T extends number> = T extends 5
  ? 4
  : T extends 4
    ? 3
    : T extends 3
      ? 2
      : T extends 2
        ? 1
        : T extends 1
          ? 0
          : never;

// 根据路径获取值类型
type GetByPath<T, K extends string> = K extends keyof T
  ? T[K]
  : K extends `${infer Key}.${infer Rest}`
    ? Key extends keyof T
      ? GetByPath<T[Key], Rest>
      : never
    : never;

// 提取插值参数
type ExtractParams<S extends string> = S extends `${string}{${infer Param}}${infer Rest}`
  ? Param extends `${infer P},${string}`
    ? P | ExtractParams<Rest>
    : Param extends `${infer P}}`
      ? P | ExtractParams<Rest>
      : Param | ExtractParams<Rest>
  : never;

// 翻译函数接口
export interface TranslateFunction<Messages extends object> {
  <K extends NestedKeyOf<Messages>>(
    key: K,
    ...args: GetByPath<Messages, K> extends string
      ? ExtractParams<GetByPath<Messages, K>> extends never
        ? []
        : [values: Record<ExtractParams<GetByPath<Messages, K>>, string | number | ReactNode>]
      : []
  ): string;

  // rich 方法用于返回 ReactNode
  rich<K extends NestedKeyOf<Messages>>(
    key: K,
    ...args: GetByPath<Messages, K> extends string
      ? ExtractParams<GetByPath<Messages, K>> extends never
        ? []
        : [values: Record<ExtractParams<GetByPath<Messages, K>>, string | number | ReactNode>]
      : []
  ): ReactNode;
}

// useTranslations 的返回类型
export type UseTranslationsReturn<Messages extends object> = TranslateFunction<Messages>;

// ============= 配置类型 =============

interface NextI18nConfig<Messages extends object> {
  messages: Messages;
  defaultLocale?: keyof Messages;
}

// ============= 导出的工厂函数 =============

export function createNextI18n<Messages extends Record<string, object>>(
  config: NextI18nConfig<Messages>,
) {
  type Locales = keyof Messages;
  type LocaleMessages = Messages[Locales];

  const defaultLocale = config.defaultLocale || (Object.keys(config.messages)[0] as Locales);

  // Provider 组件
  function NextI18nProvider({
    children,
    locale = defaultLocale,
  }: {
    children: ReactNode;
    locale?: Locales;
  }) {
    const messages = config.messages[locale];

    return (
      <NextIntlClientProvider locale={locale as string} messages={messages as any}>
        {children}
      </NextIntlClientProvider>
    );
  }

  // useTranslations hook with explicit return type
  const useTranslations: {
    <NS extends NestedKeyOf<LocaleMessages>>(
      namespace: NS,
    ): UseTranslationsReturn<
      GetByPath<LocaleMessages, NS> extends object ? GetByPath<LocaleMessages, NS> : LocaleMessages
    >;
    (): UseTranslationsReturn<LocaleMessages>;
  } = (namespace?: string) => {
    const t = useNextIntlTranslations(namespace);

    // 创建类型安全的包装函数
    const typedT = Object.assign(
      (key: string, values?: any) => {
        return t(key, values);
      },
      {
        rich: (key: string, values?: any) => {
          return t.rich(key, values);
        },
      },
    );

    return typedT;
  };

  return {
    NextI18nProvider,
    useTranslations,
  };
}
