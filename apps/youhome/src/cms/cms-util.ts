// cms 工具集

import { isLocalRuntime, isPreviewServices } from '@repo/common/env/index';
import { stringify } from 'qs-esm';
import type { Changelog, Media, Post, UseCase, Video } from './types/payload-raw-types';
import {
  type CollectionResponse,
  CollectionType,
  type CollectionTypeMap,
  type RichTextContent,
} from './types/payload-types';

type WhereOperator = {
  equals?: string | number | boolean;
  not_equals?: string | number | boolean;
  greater_than?: number;
  greater_than_equal?: number;
  less_than?: number;
  less_than_equal?: number;
  like?: string;
  contains?: string;
  in?: (string | number)[];
  not_in?: (string | number)[];
  exists?: boolean;
  near?: [number, number, number?, number?];
  within?: Record<string, unknown>;
  intersects?: Record<string, unknown>;
};

type WhereClause = {
  [key: string]: WhereOperator | WhereClause;
};

interface FetchCollectionItemsParams {
  locale?: string;
  depth?: number;
  page?: number;
  limit?: number;
  draft?: boolean;
  where?: WhereClause;
  sort?: string | string[];
}

interface FetchCollectionSingleItemParams {
  locale?: string;
  depth?: number;
  draft?: boolean;
  where?: WhereClause;
  slug?: string;
  id?: string;
}

// Next.js 缓存配置接口（适配 Cloudflare）
interface NextFetchOptions {
  next?: {
    revalidate?: number | false;
    // 移除 tags，在 Cloudflare 环境下使用时间基础的重新验证
  };
}

async function getJWTToken() {
  const user = await fetch(`${process.env.CMS_HOST}/api/users/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    cache: 'no-store',
    body: JSON.stringify({
      email: process.env.CMS_EMAIL,
      password: process.env.CMS_PWD,
    }),
  }).then((req) => req.json());

  return user.token;
}

const processLocale = (locale?: string) => {
  if (!locale) return undefined;
  return locale.split('-')[0];
};

export const fetchCollectionItems = async <T extends CollectionType>(
  collection: T,
  params: FetchCollectionItemsParams = {},
  options?: NextFetchOptions,
): Promise<CollectionResponse<CollectionTypeMap[T]>> => {
  const {
    locale,
    depth = 2,
    page,
    limit,
    draft = isLocalRuntime() || isPreviewServices(),
    // draft = false,
    where,
    sort,
  } = params;
  const processedLocale = processLocale(locale);

  const query = {
    ...(processedLocale && { locale: processedLocale }),
    ...(depth && { depth }),
    ...(page && { page }),
    ...(limit && { limit }),
    ...(draft !== undefined && { draft }),
    ...(where && { where }),
    ...(sort && { sort: Array.isArray(sort) ? sort.join(',') : sort }),
  };

  const stringifiedQuery = stringify(query, { addQueryPrefix: true });

  const headers: Record<string, string> = {};

  if (draft) {
    const token = await getJWTToken();
    headers.Authorization = `JWT ${token}`;
  }

  const url = `${process.env.CMS_HOST}/api/${collection}${stringifiedQuery}`;

  // Cloudflare 环境下的缓存配置
  const fetchOptions: RequestInit & NextFetchOptions = {
    headers,
    // 在开发环境或草稿模式下禁用缓存
    ...(isLocalRuntime() || draft ? { cache: 'no-store' } : {}),
    // 合并用户传入的选项
    ...(options || {}),
  };

  // 为生产环境设置默认的时间基础重新验证
  if (!isLocalRuntime() && !draft && !fetchOptions.next?.revalidate) {
    if (!fetchOptions.next) {
      fetchOptions.next = {};
    }
    // 默认 5 分钟重新验证，可根据实际需求调整
    fetchOptions.next.revalidate = 300;
  }

  const response = await fetch(url, fetchOptions);

  if (!response.ok) {
    throw new Error(`Failed to fetch collection items: ${response.statusText}`);
  }

  const data = await response.json();
  // console.log("dataaaaa", data);
  return data;
};

export const fetchCollectionSingleItem = async <T extends CollectionType>(
  collection: T,
  params: FetchCollectionSingleItemParams = {},
  options?: NextFetchOptions,
): Promise<CollectionTypeMap[T]> => {
  const {
    locale,
    depth = 2,
    draft = isLocalRuntime() || isPreviewServices(),
    // draft = false,
    where,
    slug,
    id,
  } = params;
  const processedLocale = processLocale(locale);

  const query = {
    ...(processedLocale && { locale: processedLocale }),
    ...(depth && { depth }),
    ...(draft !== undefined && { draft }),
    ...(where && { where }),
    ...(slug && { where: { ...where, slug: { equals: slug } } }),
  };

  const stringifiedQuery = stringify(query, { addQueryPrefix: true });

  const headers: Record<string, string> = {};

  if (draft) {
    const token = await getJWTToken();
    headers.Authorization = `JWT ${token}`;
  }

  const url = `${process.env.CMS_HOST}/api/${collection}${id ? `/${id}` : ''}${stringifiedQuery}`;

  // Cloudflare 环境下的缓存配置
  const fetchOptions: RequestInit & NextFetchOptions = {
    headers,
    // 在开发环境或草稿模式下禁用缓存
    ...(isLocalRuntime() || draft ? { cache: 'no-store' } : {}),
    // 合并用户传入的选项
    ...(options || {}),
  };

  // 为生产环境设置默认的时间基础重新验证
  if (!isLocalRuntime() && !draft && !fetchOptions.next?.revalidate) {
    if (!fetchOptions.next) {
      fetchOptions.next = {};
    }
    // 单个内容项缓存时间可以更长，默认 10 分钟
    fetchOptions.next.revalidate = 600;
  }

  const response = await fetch(url, fetchOptions);

  // console.log("urllll!!!", url, fetchOptions);

  if (!response.ok) {
    throw new Error(`Failed to fetch collection item: ${response.statusText}`);
  }

  const data = await response.json();
  return id ? data : data.docs[0];
};

export function getSupportedLocales() {
  return ['en'];
}

// 定义包含text属性的接口
interface TextNode {
  type: string;
  text: string;
  [key: string]: unknown;
}

export function parseRichTextToString(content: RichTextContent) {
  if (!content || !content.root || !Array.isArray(content.root.children)) {
    return '';
  }

  // 遍历所有子节点，提取文本内容
  return content.root.children.reduce((text, child) => {
    // 处理段落节点
    if (child.type === 'paragraph' && Array.isArray(child.children)) {
      // 提取段落中的文本内容
      const paragraphText = child.children.reduce((paragraphContent, textNode) => {
        // 普通文本节点
        if (textNode.type === 'text' && 'text' in textNode) {
          // 使用类型断言，转换为我们定义的接口
          const nodeText = (textNode as TextNode).text;
          if (typeof nodeText === 'string') {
            return paragraphContent + nodeText;
          }
        }
        return paragraphContent;
      }, '');

      // 如果有内容，添加到总文本中，段落之间添加空格
      return text + (text && paragraphText ? ' ' : '') + paragraphText;
    }
    return text;
  }, '');
}

export type MediaType = {
  type: 'image' | 'video';
  thumbnailUrl?: string;
};

export function getMediaThumbnail(obj: Video | Media): MediaType | null {
  if ((obj as Video)?.cloudflareStream?.streamId) {
    return {
      type: 'video',
      thumbnailUrl: ((obj as Video).poster as Media).url!,
    };
  } else if ((obj as Media)?.url) {
    return {
      type: 'image',
      thumbnailUrl: obj.url!,
    };
  }

  return null;
}

export function genPageMetadata(post: Post | UseCase | Changelog, titleSuffix?: string) {
  const desc = parseRichTextToString(post.content);
  const finalTitle = post?.meta?.title || post.title;
  const finalDesc = post?.meta?.description || desc;

  const videoPoster =
    // medium 是 900 * 450。thumbnail 是 300 * 150，会有点糊
    (((post as UseCase | Changelog)?.heroVideo as Video)?.poster as Media)?.sizes?.medium?.url ||
    (((post as UseCase | Changelog)?.heroVideo as Video)?.poster as Media)?.thumbnailURL;
  const mediaPoster =
    (post?.heroImage as Media)?.sizes?.medium?.url || (post?.heroImage as Media)?.thumbnailURL;
  const imageUrl = videoPoster ? videoPoster : mediaPoster || '/cover.png';

  return {
    title: `${finalTitle}${titleSuffix ? titleSuffix : ''}`,
    description: finalDesc.slice(0, 150),
    twitter: {
      title: finalTitle,
      description: finalDesc,
      card: 'summary_large_image',
      images: [{ url: imageUrl }],
    },
    openGraph: {
      title: finalTitle,
      description: finalDesc,
      images: [
        {
          url: imageUrl,
        },
      ],
    },
  };
}

export const collectionPathMap = {
  [CollectionType.Changelogs]: 'updates',
  [CollectionType.Posts]: 'blog',
  [CollectionType.UseCases]: 'use-cases',
};

const SITE_URL =
  process.env.YOUMIND_HOST || process.env.VERCEL_PROJECT_PRODUCTION_URL || 'https://youmind.com';

export async function genSitemapConfig(
  collection: CollectionType.Changelogs | CollectionType.Posts | CollectionType.UseCases,
) {
  const result = await fetchCollectionItems(collection, { limit: 9999 });
  const dateFallback = new Date().toISOString();

  const sitemap = result.docs
    ? result.docs
        .filter((p) => Boolean(p?.slug))
        .map((p) => ({
          loc: `${SITE_URL}/${collectionPathMap[collection]}/${p?.slug}`,
          lastmod: p.updatedAt || dateFallback,
        }))
    : [];

  sitemap.push({
    loc: `${SITE_URL}/${collectionPathMap[collection]}`,
    lastmod: dateFallback,
  });

  if (collection === CollectionType.UseCases) {
    const useCasesCategories = await fetchCollectionItems(CollectionType.UseCasesCategories, {
      sort: 'sort',
    });

    useCasesCategories.docs.forEach((category) => {
      sitemap.push({
        loc: `${SITE_URL}/${collectionPathMap[collection]}/category/${category?.slug}`,
        lastmod: category.updatedAt || dateFallback,
      });
    });
  }

  return sitemap;
}
