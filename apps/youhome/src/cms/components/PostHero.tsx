'use client';
import { Stream, type StreamPlayerApi } from '@cloudflare/stream-react';
import Image from 'next/image';
import { useRef, useState } from 'react';

import { ArrowRightIcon } from '@/components/icon/arrow';
import { cn } from '@/lib/utils';

import type { Media } from '../types';

export interface VideoType {
  cloudflareStream?: {
    streamId: string;
  };
  poster?: {
    url?: string | null;
  } | null;
}

export interface ImageType {
  url?: string | null;
  alt?: string | null;
}

interface PostHeroProps {
  heroVideo?: VideoType;
  heroImage?: ImageType;
  title?: string;
  className?: string;
}

export function PostHero({ heroVideo, heroImage, title = '', className }: PostHeroProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [userClickedImage, setUserClickedImage] = useState(false);
  const streamRef = useRef<StreamPlayerApi | undefined>(undefined);
  const autoPlayTriggeredRef = useRef(false);

  if (!heroVideo && !heroImage) {
    return null;
  }

  return (
    <div className={cn('mx-auto mt-10 max-w-[1200px] md:mt-[50px]', className)}>
      {heroVideo?.cloudflareStream?.streamId ? (
        <div
          className={cn(
            'group relative aspect-video w-full overflow-hidden rounded-lg border border-border/70',
            // !isPlaying ? "border-border/70" : "border-transparent",
          )}
        >
          <Stream
            streamRef={streamRef}
            controls
            src={heroVideo.cloudflareStream.streamId}
            // poster={heroVideo.poster?.url || undefined}
            letterboxColor="transparent"
            className="!h-full !w-full !pt-0"
            onPlay={() => {
              setIsPlaying(true);
            }}
            onCanPlay={() => {
              // 如果用户点击过图片且自动播放尚未触发，则播放视频
              if (userClickedImage && !autoPlayTriggeredRef.current) {
                streamRef.current?.play();
                autoPlayTriggeredRef.current = true;
              }
            }}
            // onPause={() => {
            //   setIsPlaying(false);
            // }}
          />
          {!isPlaying && (
            <>
              <Image
                placeholder="blur"
                src={heroVideo.poster?.url || ''}
                alt={(heroVideo?.poster as Media)?.alt || ''}
                fill
                // 移动端直接透过点击，否则微信播放可能播放状态不同步（播放中，但是按钮仍然是待播放）。
                className="pointer-events-none h-full w-full cursor-pointer object-cover transition-transform duration-300 group-hover:scale-105 md:pointer-events-auto"
                onClick={() => {
                  setUserClickedImage(true);
                  streamRef.current?.play();
                }}
              />
              <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
                <div className="flex h-[72px] w-[72px] items-center justify-center rounded-full bg-white/60 shadow-md backdrop-blur-sm transition-colors">
                  <ArrowRightIcon size={20} className="text-black" />
                </div>
              </div>
            </>
          )}
        </div>
      ) : heroImage?.url ? (
        <div className="relative aspect-video w-full overflow-hidden rounded-lg">
          <Image
            src={heroImage.url}
            alt={heroImage.alt || title}
            fill
            priority
            className="w-full object-cover"
          />
        </div>
      ) : null}
    </div>
  );
}
