'use client';

import { InternalError } from '@repo/ui/components/custom/internal-error';
import { Toaster } from '@repo/ui/components/ui/sonner';
import './globals.css';
import * as Sentry from '@sentry/nextjs';
import { useLocale } from 'next-intl';
import { useEffect } from 'react';
import { inter } from '@/lib/fonts';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // 根据error.digest判断是否来自服务端
  const from = error.digest ? 'server' : 'client';
  const locale = useLocale();

  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <html lang={locale} className={`${inter.variable} font-sans`}>
      <body>
        <InternalError error={error} from={from} />
        <Toaster />
      </body>
    </html>
  );
}
