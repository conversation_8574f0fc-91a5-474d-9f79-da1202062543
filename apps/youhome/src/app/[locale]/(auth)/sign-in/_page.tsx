'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import appleIcon from '@public/login/apple-light.svg';
import googleIcon from '@public/login/google.svg';
import { SignInWithOAuthProvider } from '@repo/api/generated-client/camel-case/index';
import { SignInWithOTPParam, signInWithOTPSchema } from '@repo/common/types/user/types';
import { Divider } from '@repo/ui/components/custom/divider';
import { Button } from '@repo/ui/components/ui/button';
import { Label } from '@repo/ui/components/ui/label';
import { toast } from '@repo/ui/components/ui/sonner';
import { debounce } from 'lodash';
import { Loader2 } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { apiClient } from '@/lib/request/client';
import { ElementClassMap, Footnote } from './components';
import LoginContainer from './container';

export default function SignInPage({
  searchParams,
}: {
  searchParams: { next: string; error?: string };
}) {
  const t = useTranslations('Auth.Login');
  const router = useRouter();
  const next = searchParams.next ? decodeURIComponent(searchParams.next) : process.env.YOUMIND_HOST;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [isAppleLoading, setIsAppleLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const form = useForm<SignInWithOTPParam>({
    resolver: zodResolver(signInWithOTPSchema, {
      errorMap: () => {
        return { message: t(`form.email.invalid`) };
      },
    }),
  });
  const { register, handleSubmit, formState, setError, trigger, getValues } = form;
  const emailError = getValues('email') && formState.errors.email?.message;
  const onSubmit = async (params: SignInWithOTPParam) => {
    setIsSubmitting(true);
    try {
      const res = await apiClient.authApi.signInWithOTP({
        next,
        email: params.email!,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      // 开始导航，保持按钮禁用状态
      setIsNavigating(true);
      router.push(res!.redirect);
    } catch (error: any) {
      setError('email', { message: error?.message });
      setIsSubmitting(false);
      return;
    }
  };

  const onChangeHandler = useCallback(
    debounce(async () => {
      await trigger('email');
    }, 500),
    [],
  );

  useEffect(() => {
    if (searchParams.error) {
      toast('Authentication failed. Please try signing in again.');
      const url = new URL(window.location.href);
      url.searchParams.delete('error');
      window.history.replaceState({}, '', url.toString());
    }
    return () => {
      onChangeHandler.cancel();
    };
  }, []);

  return (
    <LoginContainer className="flex flex-col justify-center">
      <div className="login-page !pt-0">
        <div className="page-title">{t('title')}</div>
        <div className="page-subtitle">{t('description')}</div>

        <div className="form-container">
          <Button
            variant="outline"
            className={`mb-4 bg-white ${ElementClassMap.button}`}
            disabled={isGoogleLoading}
            onClick={async () => {
              setIsGoogleLoading(true);
              try {
                const res = await apiClient.authApi.signInWithOAuth({
                  next,
                  timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                  provider: SignInWithOAuthProvider.google,
                });
                router.push(res!.redirect);
              } catch (error: any) {
                setIsGoogleLoading(false);
                return;
              }
            }}
          >
            {isGoogleLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Image
                src={googleIcon}
                width={20}
                height={20}
                alt={t('actionByOAuth', { provider: 'Google' })}
                className="mr-2"
              />
            )}
            {t('actionByOAuth', { provider: 'Google' })}
          </Button>
          <Button
            variant="outline"
            className={`bg-white ${ElementClassMap.button}`}
            disabled={isAppleLoading}
            onClick={async () => {
              setIsAppleLoading(true);

              try {
                const res = await apiClient.authApi.signInWithOAuth({
                  next,
                  timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                  provider: SignInWithOAuthProvider.apple,
                });
                router.push(res!.redirect);
              } catch (error: any) {
                setIsAppleLoading(false);
                return;
              }
            }}
          >
            {isAppleLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Image
                src={appleIcon}
                width={20}
                height={20}
                alt={t('actionByOAuth', { provider: 'Apple' })}
                className="mr-2"
              />
            )}
            {t('actionByOAuth', { provider: 'Apple' })}
          </Button>
          <Divider className="mb-3 mt-3" />
          <form onSubmit={handleSubmit(onSubmit)}>
            <Label htmlFor="email" className="hidden">
              {t('form.email.label')}
            </Label>
            <input
              {...register('email', {
                onChange: onChangeHandler,
              })}
              type="email"
              className={`w-full ${ElementClassMap.input}`}
              placeholder={t('form.email.placeholder')}
            />
            {emailError && <div className="mt-1 text-sm text-error">{emailError}</div>}
            <Button
              type="submit"
              className={`mt-4 ${ElementClassMap.button} ${ElementClassMap.withBrand}`}
              disabled={isSubmitting || isNavigating || !formState.isValid}
            >
              {isSubmitting || isNavigating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('actionLoading')}
                </>
              ) : (
                t('action')
              )}
            </Button>
            <Footnote />
          </form>
        </div>
      </div>
    </LoginContainer>
  );
}
