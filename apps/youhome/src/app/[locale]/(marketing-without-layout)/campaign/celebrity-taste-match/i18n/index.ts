import { createNextI18n } from '@/i18n/factory';

const { NextI18nProvider, useTranslations } = createNextI18n({
  messages: {
    'en-US': {
      CelebrityTasteMatch: {
        badge: 'AI-Powered Personality Matching',
        mainTitle: 'Discover Your Celebrity Twin',
        subtitle:
          'Based on your unique way of expressing yourself, our AI will match you with public figures who share a similar sense of taste and personality.',
        tagline: 'Different on the outside, kindred in spirit.',
        inputPlaceholder: 'Enter your Twitter handle',
        startButton: 'Start Matching',
        loadingTitle: 'AI is analyzing your tweets...',
        loadingSubtitle:
          'Deeply analyzing your expression style, matching celebrities with similar tastes, this may take a few seconds',
        progressSteps: {
          step1: 'Fetching tweet data',
          step2: 'AI deep analysis',
          step3: 'Generating match results',
        },
        progressTip:
          'Tip: We are analyzing your language style, emotional expression and personality traits',
      },
      AnalysisResult: {
        title: 'Celebrity Match Results',
        subtitle:
          "Based on your expression style, AI has matched you with celebrities of similar taste. Discover the 'celebrity soul' that resonates with your inner style.",
        soulFormula: {
          title: 'Soul Formula',
          tagline:
            "Your soul formula is a self-taught doer who meets a philosopher contemplating the universe, plus a pinch of 'whatever, let's go live' recklessness.",
        },
        launchCard: {
          title: "Launch Instructions for 'Future Architect'",
          closingThought:
            "Your code either changes the world or teaches you where the next bug is—either way, it's not wasted.",
        },
        growthCard: {
          summary:
            "You are a future architect with blueprints but trapped by the gravity of reality. You have both the depth of thinking of an academic and the keen sense of smell of a Degen. The only enemy is the voice inside you that says 'wait a little longer'.",
        },
      },
      Error: {
        analysisFailed: 'Analysis failed',
        unknownError: 'Unknown error',
      },
    },
  },
});

export { useTranslations, NextI18nProvider };
