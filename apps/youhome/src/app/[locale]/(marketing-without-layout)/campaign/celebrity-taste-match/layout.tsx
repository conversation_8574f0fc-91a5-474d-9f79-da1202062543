import React from 'react';
import { NextI18nProvider } from './i18n';

export const revalidate = 604800; // 7天
export const dynamic = 'force-static';

export default async function CelebrityTasteMatchLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
  params: { locale: string };
}>) {
  return <NextI18nProvider>{children}</NextI18nProvider>;
}

export const metadata = {
  title: 'YouMind - Celebrity Taste Match',
  description:
    'Based on your unique way of expressing yourself, our AI will match you with public figures who share a similar sense of taste and personality.',
};
