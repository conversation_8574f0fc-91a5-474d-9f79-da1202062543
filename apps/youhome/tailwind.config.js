/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: ['./src/**/*.{ts,tsx}', './node_modules/@repo/ui/src/components/**/*.{ts,tsx}'],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '1rem',
      screens: {
        xl: '1232px', // 1200px + 2rem
      },
    },
    fontFamily: {
      sans: [
        'var(--font-inter)',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'Segoe UI',
        'San Francisco Pro',
        'Roboto',
        'sans-serif',
        'Microsoft YaHei',
        'PingFang SC',
        'Source Han Sans SC',
        'Noto Sans SC',
      ],
      'sans-title': [
        'var(--font-cereal)',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'Segoe UI',
        'San Francisco Pro',
        'Roboto',
        'sans-serif',
        'Microsoft YaHei',
        'PingFang SC',
        'Source Han Sans SC',
        'Noto Sans SC',
      ],
      serif: [
        'ui-serif',
        'Georgia',
        'Cambria',
        'Times New Roman',
        'Times',
        'serif',
        'SimSun',
        'Songti SC',
        'Noto Serif SC',
      ],
      roman: ['Times New Roman', 'Times', 'ui-serif', 'Georgia', 'Cambria', 'serif'],
      mono: [
        'ui-monospace',
        'SFMono-Regular',
        'Menlo',
        'Monaco',
        'Consolas',
        'Liberation Mono',
        'Courier New',
        'monospace',
      ],
      ddin: ['D-DIN'],
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsla(var(--input))',
        ring: 'hsl(var(--ring))',
        divider: 'hsla(var(--divider))',
        'card-snips': 'hsl(var(--card-snips))',
        'card-muted': 'hsla(var(--card-muted))',
        error: 'hsl(var(--error))',
        background: 'hsl(var(--background))',
        foreground: 'hsla(var(--foreground))',
        'assistant-icon': 'hsl(var(--assistant-icon))',
        'global-background': 'hsl(var(--global-background))',
        block: {
          background: 'hsl(var(--block-background))',
        },
        rich: {
          'panel-background': 'hsl(var(--rich-panel-background))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        link: {
          DEFAULT: 'hsl(var(--link))',
        },
        tertiary: {
          DEFAULT: 'hsl(var(--tertiary))',
          foreground: 'hsl(var(--tertiary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsla(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsla(var(--muted))',
          foreground: 'hsla(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsla(var(--accent))',
          foreground: 'hsla(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsla(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsla(var(--card-foreground))',
        },
        caption: {
          DEFAULT: 'hsl(var(--caption))',
          foreground: 'hsl(var(--caption))',
        },
        disabled: {
          DEFAULT: 'hsl(var(--disabled))',
          foreground: 'hsl(var(--disabled))',
        },
        inactive: 'hsla(var(--inactive))',
        brand: {
          DEFAULT: 'hsl(var(--brand))',
          hover: 'hsl(var(--brand-hover))',
          pressed: 'hsl(var(--brand-pressed))',
          disabled: 'hsl(var(--brand-disabled))',
          foreground: 'hsl(var(--brand-foreground))',
        },
        orange: {
          DEFAULT: 'hsl(var(--orange-color))',
        },
        'highlighted-subtitle': 'rgba(88, 89, 209, 0.24)',
        'color-1': 'hsl(var(--color-1))',
        'color-2': 'hsl(var(--color-2))',
        'color-3': 'hsl(var(--color-3))',
        'color-4': 'hsl(var(--color-4))',
        'color-5': 'hsl(var(--color-5))',
      },
      boxShadow: {
        sm: '0 1px 4px 0 rgba(2,4,26,0.06)',
        md: '0 2px 8px 0 rgba(2,4,26,0.06)',
        lg: '0 4px 20px 0 rgba(2,4,26,0.16)',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        rotate: {
          '0%': {
            transform: 'rotate(0deg)',
          },
          '100%': {
            transform: 'rotate(-360deg)',
          },
        },
        rotate2: {
          '0%': {
            transform: 'rotate(0deg)',
          },
          '100%': {
            transform: 'rotate(360deg)',
          },
        },
        'slide-in-from-left': {
          '0%': {
            transform: 'translateX(-100%)',
          },
          '100%': {
            transform: 'translateX(0)',
          },
        },
        'slide-out-to-left': {
          '0%': {
            transform: 'translateX(0)',
          },
          '100%': {
            transform: 'translateX(-100%)',
          },
        },
        shimmer: {
          '0%': {
            backgroundPosition: '200% 0',
          },
          '100%': {
            backgroundPosition: '-200% 0',
          },
        },
        gradient: {
          '0%': {
            transform: 'translateX(-100%)',
          },
          '100%': {
            transform: 'translateX(100%)',
          },
        },
        rainbow: {
          '0%': {
            'background-position': '0%',
          },
          '100%': {
            'background-position': '200%',
          },
        },
        shine: {
          '0%': {
            'background-position': '0% 0%',
          },
          '50%': {
            'background-position': '100% 100%',
          },
          to: {
            'background-position': '0% 0%',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'slide-in-from-left': 'slide-in-from-left 0.2s ease-out',
        rotate: 'rotate 5s linear infinite',
        rotate2: 'rotate2 5s linear infinite',
        'slide-out-to-left': 'slide-out-to-left 0.2s ease-out forwards',
        shimmer: 'shimmer 2s linear infinite',
        gradient: 'gradient 1.5s linear infinite',
        rainbow: 'rainbow var(--speed, 2s) infinite linear',
        marquee: 'marquee var(--duration) infinite linear',
        'marquee-vertical': 'marquee-vertical var(--duration) linear infinite',
        shine: 'shine var(--duration) infinite linear',
      },
      backgroundImage: {
        'ai-pattern':
          'linear-gradient(hsl(var(--background)),hsl(var(--background))),conic-gradient(from 180deg at 50% 50%, #2986FF -41.75deg, #E681F8 22.49deg, #EB41E3 45.16deg, #864BFF 128.21deg, #63F7FD 223.34deg, #2986FF 318.25deg, #E681F8 382.49deg)',
        'twitter-pattern':
          'linear-gradient(hsl(var(--background)),hsl(var(--background))),linear-gradient(260.64deg,rgba(29,96,249,0.6)5.53%,rgba(50,206,224,0.6)88.65%),linear-gradient(164.18deg, rgba(78,233,249,0.6) 3.09%, rgba(49,229,179,0.6) 98.04%),linear-gradient(198.97deg, #4025E6 2.33%, #F3F63C 90.57%)',
        'action-light':
          'linear-gradient(173.8deg, rgba(255, 255, 255, 0.8) 4.9%, rgba(255, 255, 255, 0.24) 92.36%)',
        'action-dark':
          'linear-gradient(157.38deg, rgba(255, 255, 255, 0.2) 7.97%, rgba(255, 255, 255, 0.06) 90.81%)',
        'action-overview': 'linear-gradient(166.81deg, #FFA503 9.49%, #FA6E1A 94.96%)',
        'action-media': 'linear-gradient(157.89deg, #EA4CBB 7.77%, #CD45F2 90.62%)',
        'action-explain': 'linear-gradient(167.24deg, #56C3FA 9.24%, #1278F5 94.95%)',
        'action-note': 'linear-gradient(160.71deg, #FFFFFF 15.28%, #CFCED6 84.57%)',
        'action-transcript': 'linear-gradient(167.24deg, #56C3FA 9.24%, #1278F5 94.95%)',
        'action-read-aloud': 'linear-gradient(167.24deg, #5AF574 9.24%, #13C130 94.95%)',
      },
      fontSize: {
        '2xs': '10px',
      },
      spacing: {
        'safe-top': 'env(safe-area-inset-top, 0px)',
        'safe-bottom': 'env(safe-area-inset-bottom, 0px)',
        'safe-left': 'env(safe-area-inset-left, 0px)',
        'safe-right': 'env(safe-area-inset-right, 0px)',
      },
      transitionProperty: {
        width: 'width',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
