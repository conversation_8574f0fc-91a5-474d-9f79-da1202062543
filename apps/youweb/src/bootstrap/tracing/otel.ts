import {
  CompositePropagator,
  W3CBaggagePropagator,
  W3CTraceContextPropagator,
} from '@opentelemetry/core';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { defaultResource, resourceFromAttributes } from '@opentelemetry/resources';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base';
import { WebTracerProvider } from '@opentelemetry/sdk-trace-web';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import { MinimalTraceFilterSpanProcessor } from './minimal-trace-filter';
import { getTraceManager, initializeTraceManager } from './trace-manager-singleton';
import { SpanType } from './trace-types';

const resource = defaultResource().merge(
  resourceFromAttributes({
    [ATTR_SERVICE_NAME]: 'youweb',
    'service.namespace': 'youmind',
    'deployment.environment.name': process.env.YOUMIND_ENV || 'local',
  }),
);

// 创建带调试的 OTLP 导出器
const baseExporter = new OTLPTraceExporter({
  url: `${window.location.origin}/telemetry`,
});

const batchProcessor = new BatchSpanProcessor(baseExporter);
const provider = new WebTracerProvider({
  resource,
  spanProcessors: [new MinimalTraceFilterSpanProcessor(batchProcessor)],
});

provider.register({
  propagator: new CompositePropagator({
    propagators: [new W3CTraceContextPropagator(), new W3CBaggagePropagator()],
  }),
});

import { instrumentFetch } from './trace-http';
import { instrumentInteractions } from './trace-interaction';
import { setupSentryIntegration } from './trace-sentry';
import { getRouteTemplateFromUrl } from './utils';

// 开始追踪
export function startTracing(): void {
  const sessionId = crypto.randomUUID();
  const sessionStartTime = Date.now();

  const sessionAttributes = {
    'session.id': sessionId,
    'session.start_url': window.location.href,
    'session.referrer': document.referrer,
    'session.user_agent': navigator.userAgent,
    'session.start_time': sessionStartTime,
  };

  const traceManager = initializeTraceManager(provider, sessionAttributes);
  const routeTemplate = getRouteTemplateFromUrl(window.location.href);
  traceManager.startRootSpan(SpanType.PAGE_LOAD, `PAGE_LOAD ${routeTemplate}`, {
    'page.url': window.location.href,
    'page.referrer': document.referrer,
    'page.ready_state': document.readyState,
  });

  instrumentFetch();
  setupSentryIntegration();
  instrumentInteractions();

  window.addEventListener('beforeunload', () => {
    traceManager.cleanup();
  });
}

export { getTraceManager };
