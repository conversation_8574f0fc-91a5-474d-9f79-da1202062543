import { Button } from '@repo/ui/components/ui/button';
import { atom, useSetAtom } from 'jotai';
import { X } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export const showInstallExtensionTipAtom = atom(false);

export const InstallExtensionTipPopUp = () => {
  const { t } = useTranslation('Sidebar');

  const setShowInstallExtensionTip = useSetAtom(showInstallExtensionTipAtom);

  const goToInstallExt = () => {
    window.open(process.env.PUBLIC_BROWSER_EXTENSION_URL, '_blank');
  };

  const handleClose = () => {
    localStorage.setItem('installExtensionTipShown', 'true');
    setShowInstallExtensionTip(false);
  };

  return (
    <div className="group relative mb-2 h-[220px] w-full rounded-xl bg-card-snips p-3">
      <Button
        variant="icon"
        size="xs"
        iconOnly
        onClick={handleClose}
        className="absolute rounded-full right-0 top-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
      >
        <X size={16} />
      </Button>
      <div className="mb-2 flex h-[88px] items-center justify-center rounded bg-background">
        <img
          src="https://cdn.gooo.ai/assets/install-extension-tip.png"
          alt=""
          className="h-[76px] w-auto object-contain"
        />
      </div>

      <div className="text-sm font-medium text-center text-foreground">
        {t('getBrowserExtension')}
      </div>

      <div className="mb-3 text-xs text-center text-muted-foreground">{t('askAndSave')}</div>

      <Button onClick={goToInstallExt} className="h-[32px] w-full rounded-full">
        {t('installExtension')}
      </Button>
    </div>
  );
};
