/* eslint-disable react/display-name */
import clsx from 'clsx';
import React, { forwardRef, HTMLAttributes } from 'react';

import type { BoardTreeItem } from '@/typings/board-item';
import { cn } from '@/utils/utils';
import { BoardItem } from '../BoardItem';
import styles from './TreeItem.module.css';

export interface Props extends Omit<HTMLAttributes<HTMLLIElement>, 'id'> {
  item: BoardTreeItem;
  childCount?: number;
  clone?: boolean;
  collapsed?: boolean;
  depth: number;
  disableInteraction?: boolean;
  disableSelection?: boolean;
  ghost?: boolean;
  isInside?: boolean;
  childrenCount?: number;
  activeItem?: BoardTreeItem | null;
  activeItemFromBelow?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleProps?: any;
  indicator?: boolean;
  indentationWidth: number;
  onCollapse?(): void;
  wrapperRef?(node: HTMLLIElement): void;
}

export const TreeItem = forwardRef<HTMLDivElement, Props>(
  (
    {
      childCount,
      clone,
      depth,
      disableSelection,
      disableInteraction,
      ghost,
      handleProps,
      item,
      indentationWidth,
      indicator,
      collapsed,
      onCollapse,
      style,
      wrapperRef,
      isInside,
      childrenCount = 0,
      activeItem,
      activeItemFromBelow,
      ...props
    },
    ref,
  ) => {
    const activeItemIsInThisGroup = activeItem?.parentId === item?.id ? 0 : 1;

    return (
      <li
        className={cn(
          styles.Wrapper,
          clone && styles.clone,
          ghost && styles.ghost,
          indicator && styles.indicator,
          disableSelection && styles.disableSelection,
          disableInteraction && styles.disableInteraction,
          'notranslate',
        )}
        ref={wrapperRef}
        id={item.id}
        data-entity-id={item.entity?.id}
        style={
          {
            '--spacing': clone ? '0px' : `${indentationWidth * depth}px`,
          } as React.CSSProperties
        }
        {...props}
      >
        {isInside && (
          <div
            className="absolute left-0 top-0 z-0 w-full rounded-[8px] border bg-card-snips"
            style={{
              height: `${(childrenCount + 1 + activeItemIsInThisGroup) * 40 - 6}px`,
              top: `${(activeItemFromBelow ? 0 : activeItemIsInThisGroup) * -40}px`,
            }}
          />
        )}

        <div ref={ref} style={style} className={clsx(styles.TreeItem, 'z-20')}>
          <div
            className={cn(styles.Inner, clone && 'relative rounded-[8px] shadow-md')}
            style={{
              opacity: ghost ? 0 : 1,
            }}
          >
            <BoardItem
              handleProps={handleProps}
              item={item}
              collapsed={collapsed}
              onCollapse={onCollapse}
              isDragging={clone}
            />
            {clone && childCount && childCount > 0 ? (
              <span className={styles.Count}>{childCount}</span>
            ) : null}
          </div>
        </div>
      </li>
    );
  },
);
