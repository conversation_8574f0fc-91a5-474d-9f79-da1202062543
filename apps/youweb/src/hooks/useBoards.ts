import { BoardStatusEnum } from '@repo/common/types/board';
import { Snip } from '@repo/ui-business-snip';
import { produce } from 'immer';
import { atom, useSetAtom } from 'jotai';
import {
  archiveBoard,
  getBoardDetail,
  listBoardsWithSomeBoardItems,
  unarchiveBoard,
} from '@/apis/board';
import { Board } from '@/typings/board';
import { BoardItem, BoardItemTypeEnum } from '@/typings/board-item';
import { formatBoardForUI } from '@/utils/board/formatBoardItemParentId';
import { callHTTP } from '@/utils/callHTTP';
import { isEditingBoardItemAtom } from './useBoardItemTree';
import { changePanelDataAtom, panelStateAtom } from './useBoardState';
import { createDedupedRequestAtom } from './useDedupRequestAtom';
import { favoritesAtom } from './useFavorite';
import { setMindStudioActiveAtom } from './useMindStudio';

const INITIAL_COLLECTION_ID = '';
export const boardIdAtom = atom(INITIAL_COLLECTION_ID);
export const boardCacheAtom = atom<Array<Board>>([]);

export const boardsAtom = atom<Board[]>((get) => {
  return get(boardCacheAtom);
});
export const pinnedBoardsAtom = atom<Board[]>((get) => {
  const cache = get(boardCacheAtom);
  return cache.filter((c) => c.pinned_at);
});
export const boardAtom = atom(
  (get) => {
    const id = get(boardIdAtom);
    const cache = get(boardCacheAtom);

    return cache.find((board) => board.id === id);
  },
  (_, set, board: Board) => {
    set(
      boardCacheAtom,
      produce((draft: Array<Board>) => {
        const index = draft.findIndex((b) => b.id === board.id);
        if (index !== -1) {
          draft[index] = {
            ...draft[index],
            ...board,
          };
        } else {
          draft.unshift(board);
        }
      }),
    );
  },
);

export const moveABoardToTopAtom = atom(null, (_get, set, boardId: string) => {
  set(
    boardCacheAtom,
    produce((draft: Array<Board>) => {
      const index = draft.findIndex((item) => item.id === boardId);
      if (index !== -1) {
        const [board] = draft.splice(index, 1);
        draft.unshift({
          ...board,
          pinned_at: new Date(),
        });
      }
    }),
  );
});

export const moveABoardToUnpinAtom = atom(null, (_get, set, boardId: string, created_at: Date) => {
  set(
    boardCacheAtom,
    produce((draft: Array<Board>) => {
      const index = draft.findIndex((item) => item.id === boardId);
      if (index !== -1) {
        const [board] = draft.splice(index, 1);

        const lastPinnedIndex = draft.findLastIndex((item) => item.pinned_at != null);

        let insertIndex = lastPinnedIndex + 1;
        while (insertIndex < draft.length && draft[insertIndex].created_at > created_at) {
          insertIndex++;
        }
        draft.splice(insertIndex, 0, {
          ...board,
          pinned_at: undefined,
        });
      }
    }),
  );
});

export const deleteBoardAtom = atom(null, (_get, set, boardId: string) => {
  set(boardCacheAtom, (cache) => {
    return cache.filter((item) => item.id !== boardId);
  });
});

export const archiveBoardAtom = atom(null, async (get, set, id: string) => {
  const { error } = await archiveBoard(id);

  if (error) {
    console.error('Error archiving board:', error);
    throw error;
  }

  set(boardCacheAtom, (cache) => {
    return cache.map((board) => {
      if (board.id === id) {
        return {
          ...board,
          status: BoardStatusEnum.OTHER,
        };
      }
      return board;
    });
  });

  if (get(boardDetailAtom)?.id === id) {
    set(boardDetailAtom, (prev) => {
      if (!prev) return null;
      return {
        ...prev,
        status: BoardStatusEnum.OTHER,
      };
    });
  }
});

export const unarchiveBoardAtom = atom(null, async (_get, set, id: string) => {
  const { error } = await unarchiveBoard(id);

  if (error) {
    console.error('Error unarchiving board:', error);
    throw error;
  }

  set(boardCacheAtom, (cache) => {
    return cache.map((board) => {
      if (board.id === id) {
        return {
          ...board,
          status: BoardStatusEnum.IN_PROGRESS,
        };
      }
      return board;
    });
  });

  set(boardDetailAtom, (prev) => {
    if (!prev) return null;
    return {
      ...prev,
      status: BoardStatusEnum.IN_PROGRESS,
    };
  });
});

export const refreshBoardsAtom = createDedupedRequestAtom(async (_get, set) => {
  const { data, error } = await listBoardsWithSomeBoardItems();

  if (error) {
    console.error('Error fetching data:', error);
    return;
  }

  if (data?.length) {
    set(boardCacheAtom, data);
  }
});

export const boardDetailAtom = atom<Board | null>(null);

export const useSetBoardDetailItems = () => {
  const setBoard = useSetAtom(boardDetailAtom);

  const setBoardItems = (updater: BoardItem[] | ((prev: BoardItem[]) => BoardItem[])) => {
    setBoard((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        items: typeof updater === 'function' ? updater(prev.items) : updater,
      };
    });
  };

  return { setBoardItems };
};

export const removeBoardItemAtom = atom(null, (_get, set, boardItemId: string) => {
  set(boardDetailAtom, (prev) => {
    if (!prev) return null;
    return {
      ...prev,
      items: prev.items.filter((item) => item.id !== boardItemId),
    };
  });
});

export const useRemoveBoardItem = () => {
  const setRemoveBoardItem = useSetAtom(removeBoardItemAtom);

  return (boardItemId: string) => {
    setRemoveBoardItem(boardItemId);
  };
};

export const refreshBoardDetailAtom = atom(
  null,
  async (get, set, boardId?: string, abortSignal?: AbortSignal) => {
    const currentBoard = get(boardDetailAtom);
    if (!currentBoard && !boardId) return;
    const { error, data } = await getBoardDetail(boardId || currentBoard!.id, abortSignal);
    if (error) {
      return;
    }

    if (data) {
      const formattedBoard = formatBoardForUI(data);
      set(boardDetailAtom, formattedBoard);
      return data;
    }
  },
);

export const syncBoardItemAtom = atom(null, (_get, set, entity: BoardItem['entity']) => {
  set(
    boardDetailAtom,
    produce((draft: Board | null) => {
      if (draft) {
        const item = draft.items.find((item) => item.entity.id === entity.id);
        if (item) {
          item.entity = entity;
        }
      }
    }),
  );
});

// 批量更新snips数据，替代refreshBoardDetail避免全量刷新
export const updateBoardDetailBySnipIds = atom(null, async (get, set, snipIds: string[]) => {
  if (!snipIds.length) return;

  const { data, error } = await callHTTP('/api/v1/snip/getSnips', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ ids: snipIds }),
  });

  if (error) {
    console.error('Error fetching snips:', error);
    return;
  }

  // 如果正在创建新的分组，则不更新
  const isEditingBoardItem = get(isEditingBoardItemAtom);
  if (isEditingBoardItem) {
    return;
  }

  if (data?.length) {
    // 批量更新每个snip
    data.forEach((snip: Snip) => {
      set(syncBoardItemAtom, snip);
    });
  }
});

// 前端添加 board item 后直接修改 boardDetail，实现数据实时更新
// 否则重新 refresh 需要一次 round trip 耗时，等待较长
export const unshiftBoardItemsAtom = atom(null, (_get, set, boardItems: BoardItem[]) => {
  set(
    boardDetailAtom,
    produce((draft: Board | null) => {
      if (draft) {
        draft.items = [...boardItems, ...draft.items];
      }
    }),
  );
});

export const addBoardItemsAndOpenAtom = atom(
  null,
  (_get, set, boardItems: BoardItem[], previousBoardItemId?: string) => {
    set(changePanelDataAtom, boardItems[0]);
    set(
      boardDetailAtom,
      produce((draft: Board | null) => {
        if (!draft) {
          return;
        }
        if (!draft.items.length) {
          set(setMindStudioActiveAtom, draft.id, true);
        }

        // 如果没有指定 previousBoardItemId，则插入到最前面
        if (!previousBoardItemId) {
          draft.items = [...boardItems, ...draft.items];
        } else {
          // 找到指定的 board item 位置，插入到其后面
          const previousItemIndex = draft.items.findIndex(
            (item) => item.id === previousBoardItemId,
          );

          if (previousItemIndex !== -1) {
            // 在指定位置后面插入新的 board items
            draft.items.splice(previousItemIndex + 1, 0, ...boardItems);
          } else {
            // 如果没找到指定的 board item，则插入到最前面
            draft.items = [...boardItems, ...draft.items];
          }
        }
      }),
    );
  },
);

export const deleteBoardItemAtom = atom(null, async (get, set, boardItemId: string) => {
  const item = get(boardDetailAtom)?.items.find((item) => item.id === boardItemId);
  if (!item) {
    return;
  }
  // 更新 board detail 状态
  set(
    boardDetailAtom,
    produce((draft: Board | null) => {
      if (draft) {
        draft.items = draft.items.filter((item) => item.id !== boardItemId);
      }
    }),
  );
  // 更新 favorite 状态
  set(favoritesAtom, (prev) => {
    return prev.filter((favorite) => favorite.entity.id !== item.entity.id);
  });
  switch (item.entity_type) {
    case BoardItemTypeEnum.snip:
      await callHTTP('/api/v1/deleteSnip', {
        method: 'POST',
        body: { id: item.entity.id },
      });
      break;
    case BoardItemTypeEnum.thought:
      await callHTTP('/api/v1/deleteThought', {
        method: 'POST',
        body: { id: item.entity.id },
      });
      break;
    case BoardItemTypeEnum.chat:
      await callHTTP('/api/v1/chat/deleteBoardChat', {
        method: 'POST',
        body: {
          chat_id: item.entity.id,
        },
      });
      break;
    default:
      break;
  }
});

export const boardItemsAtom = atom((get) => {
  const boardDetail = get(boardDetailAtom);
  if (!boardDetail) return [];
  return boardDetail.items;
});

export const getActiveBoardItemAtom = atom((get) => {
  const boardDetail = get(boardDetailAtom);
  if (!boardDetail) return null;
  const panelData = get(panelStateAtom);
  if (!panelData.panelData) return null;
  const { id: board_item_id } = panelData.panelData;
  if (!board_item_id) return null;
  return boardDetail.items.find((item) => item.id === board_item_id);
});

export const getBoardItemByEntityIdAtom = atom((get) => (itemId: string) => {
  const boardDetail = get(boardDetailAtom);
  if (!boardDetail) return null;
  return boardDetail.items.find((item) => item.entity.id === itemId);
});

export const getBoardItemByIdAtom = atom((get) => (itemId: string) => {
  const boardDetail = get(boardDetailAtom);
  if (!boardDetail) return null;

  return boardDetail.items.find((item) => item.id === itemId);
});

// 在 workspace breadcrumb 中重命名时，记录正在重命名的 board item id
export const renamingBreadcrumbItemIdAtom = atom<string | null>(null);
