import { toast } from '@repo/ui/components/ui/sonner';
import { scopedAtom } from '@repo/ui/hooks/useScopedAtom';
import type { Snip, SnipVideo } from '@repo/ui-business-snip';
import { SnipTypeEnum, snipDetailAtom, useSnipDetailListener } from '@repo/ui-business-snip';
import { produce } from 'immer';
import { atomWithStorage } from 'jotai/utils';
import { apiClient, callAPI } from '@/utils/callHTTP';
import { isMedium, isPodcast, isWikipedia, isYouTube } from '@/utils/utils';

export { snipDetailAtom, useSnipDetailListener };

function sortSnips(snips: Snip[]): Snip[] {
  return snips.sort((a, b) => {
    return a.created_at > b.created_at ? -1 : 1;
  });
}

export const snipCacheAtom = scopedAtom<Array<Snip>>([]);
export const snipsAtom = scopedAtom<Snip[]>((get) => {
  return get(snipCacheAtom);
});

export const snipInProgressCacheAtom = scopedAtom<Record<string, Snip>>({});
export const snipsInProgressAtom = scopedAtom<Snip[]>((get) => {
  const cache = get(snipInProgressCacheAtom);
  return sortSnips(Object.values(cache));
});

export const setSnipAtom = scopedAtom(null, (_, set, snip: Snip) => {
  set(
    snipCacheAtom,
    produce((draft: Array<Snip>) => {
      const index = draft.findIndex((b) => b.id === snip.id);
      if (index !== -1) {
        draft[index] = {
          ...draft[index],
          ...snip,
        } as Snip;
      } else {
        draft.unshift(snip);
      }
    }),
  );
});

export const refreshSnipsAtom = scopedAtom(null, async (get, set, ids?: string[]) => {
  const param: { ids?: string[] } = {};
  if (ids?.length) {
    param.ids = ids;
  }
  const { data, error } = await callAPI(
    apiClient.snipApi.listSnips({
      body: param,
    }),
  );
  if (error) {
    return;
  }
  if (data) {
    const snips = data as Snip[];
    // 原有的更新逻辑
    if (param.ids) {
      const prevSnips = get(snipCacheAtom);
      const updatedSnips = prevSnips.map((snip) => {
        const updatedSnip = snips.find((s) => s.id === snip.id);
        return updatedSnip || snip;
      });
      set(snipCacheAtom, updatedSnips);
    } else {
      set(snipCacheAtom, snips);
    }
  }
});

export const unshiftSnipsAtom = scopedAtom(null, (get, set, snips: Snip[]) => {
  set(snipCacheAtom, [...snips, ...get(snipsAtom)]);
});

// 删除某一个 snip
export const deleteSnipAtom = scopedAtom(null, async (get, set, id: string) => {
  set(
    snipCacheAtom,
    get(snipsAtom).filter((snip) => snip.id !== id),
  );
  const { error } = await callAPI(
    apiClient.snipApi.deleteSnip({
      id,
    }),
  );
  if (error) {
    // 这里不能 throw Error，外层 catch 不到
    toast('Failed to delete snip');
  }
});

export interface SnipWithTitle extends Snip {
  title?: string;
}

export interface ImageSnippet extends SnipWithTitle {
  description?: string;
  extracted_text?: string;
}

// useSnipDetailListener is used to listen to the changes of snipDetailAtom
// especially in board split view

export const snipDetailLoadingAtom = scopedAtom<boolean>(false);

export const refreshSnipDetailAtom = scopedAtom(null, async (get, set) => {
  const currentBoard = get(snipDetailAtom);
  set(snipDetailLoadingAtom, true);
  const { data } = await callAPI(
    apiClient.snipApi.getSnip({
      id: currentBoard!.id,
    }),
  );
  const snip = data as Snip;
  if (snip) {
    // check current atom id because user may have switched to other board
    if (snip.id === get(snipDetailAtom)!.id) {
      set(snipDetailAtom, snip);
    }
  }
  set(snipDetailLoadingAtom, false);
});

export const snipTypeAtom = atomWithStorage<
  | 'unused'
  | 'all'
  | 'webpages'
  | 'images'
  | 'snippets'
  | 'youtube'
  | 'medium'
  | 'wikipedia'
  | 'podcasts'
>('snipFilterType', 'unused');
export const filteredSnipsAtom = scopedAtom<Snip[]>((get) => {
  const snips = get(snipsAtom);
  const type = get(snipTypeAtom);
  if (!snips.length) {
    return [];
  }
  if (type === 'unused') {
    return snips.filter((snip) => !snip.board_ids?.length);
  } else if (type === 'all') {
    return snips;
  } else if (type === 'webpages') {
    return snips.filter(
      (snip) =>
        snip.type === SnipTypeEnum.article ||
        snip.type === SnipTypeEnum.otherWebpage ||
        snip.type === SnipTypeEnum.unknownWebpage,
    );
  } else if (type === 'images') {
    return snips.filter((snip) => snip.type === SnipTypeEnum.image);
  } else if (type === 'snippets') {
    return snips.filter((snip) => snip.type === SnipTypeEnum.snippet);
  } else if (type === 'youtube') {
    return snips.filter((snip) => isYouTube(snip as SnipVideo));
  } else if (type === 'medium') {
    return snips.filter((snip) => isMedium(snip));
  } else if (type === 'podcasts') {
    return snips.filter((snip) => isPodcast(snip));
  } else if (type === 'wikipedia') {
    return snips.filter((snip) => isWikipedia(snip));
  }
  return [];
});

export const mobileSimpleViewAtom = scopedAtom<boolean>(false);
