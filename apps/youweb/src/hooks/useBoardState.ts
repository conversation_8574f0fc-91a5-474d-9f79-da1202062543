import { BoardItemTypeEnum } from '@repo/common/types/board-item';
import { produce } from 'immer';
import { atom, useAtomValue, useSetAtom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import type { Board } from '@/typings/board';
import type { BoardItem } from '@/typings/board-item';
import { callHTTP } from '@/utils/callHTTP';
import { useFlattenBoardItemTree } from './useBoardItemTree/useFlattenBoardItemTree';
import { boardDetailAtom, getBoardItemByEntityIdAtom, removeBoardItemAtom } from './useBoards';
import { resetActiveToolAtom } from './useMindStudio';

export interface Material extends Pick<BoardItem, 'id' | 'entity_type' | 'entity'> {}

export interface PanelState {
  panelData: Material | undefined;
}

function getInitialPanelState(): PanelState {
  return {
    panelData: undefined,
  };
}

export const boardSidebarActiveMapAtom = atomWithStorage<Record<string, boolean>>(
  'boardSidebarActiveMap',
  {},
);

export const getBoardSidebarActiveAtom = atom((get) => (boardId: string) => {
  const boardSidebarActive = get(boardSidebarActiveMapAtom);
  return boardSidebarActive[boardId] ?? true;
});

export const setBoardSidebarActiveAtom = atom(
  null,
  (_get, set, boardId: string, active: boolean) => {
    set(
      boardSidebarActiveMapAtom,
      produce((draft) => {
        draft[boardId] = active;
      }),
    );
  },
);

export const boardSidebarWidthMapAtom = atomWithStorage<Record<string, number>>(
  'boardSidebarWidthMap',
  {},
);

export const getBoardSidebarWidthAtom = atom((get) => (boardId: string) => {
  const boardSidebarWidth = get(boardSidebarWidthMapAtom);
  return boardSidebarWidth[boardId] || 240;
});

export const setBoardSidebarWidthAtom = atom(null, (_get, set, boardId: string, width: number) => {
  set(
    boardSidebarWidthMapAtom,
    produce((draft) => {
      draft[boardId] = width;
    }),
  );
});

export const boardSidebarDraggingAtom = atom(false);

export const isKeyBoardNavigatingAtom = atom(false);

export const panelStateAtom = atom<PanelState>({
  panelData: undefined,
});

export const materialReadCountAtom = atom<Record<string, number>>({});

export const readMaterialAtom = atom(null, (_get, set, materialId?: string) => {
  if (!materialId) return;
  set(
    materialReadCountAtom,
    produce((draft) => {
      if (draft[materialId]) {
        draft[materialId]++;
      } else {
        draft[materialId] = 1;
      }
    }),
  );
});

export const useTopXMaterials = (filteredMaterialIds: string[], topX: number) => {
  const { flattenedItems } = useFlattenBoardItemTree(true);
  const materialReadCount = useAtomValue(materialReadCountAtom);

  const topXMaterials = useMemo(() => {
    return flattenedItems
      .filter((item) => !filteredMaterialIds.includes(item.id))
      .sort((a, b) => (materialReadCount[b.id] ?? 0) - (materialReadCount[a.id] ?? 0))
      .slice(0, topX);
  }, [flattenedItems, filteredMaterialIds, materialReadCount, topX]);

  return topXMaterials;
};

export const clearPanelDataAtom = atom(null, (_get, set) => {
  set(
    panelStateAtom,
    produce((draft) => {
      draft.panelData = undefined;
    }),
  );
});

const searchParams = new URLSearchParams(window.location.search);
const entityType = searchParams.get('entity-type');
const entityId = searchParams.get('entity-id');

export const needToFocusMaterialAtom = atom<{
  entity_type: BoardItemTypeEnum;
  entity_id: string;
} | null>(
  entityType && entityId
    ? {
        entity_type: entityType as BoardItemTypeEnum,
        entity_id: entityId,
      }
    : null,
);

export const exitBoardWorkSpaceAtom = atom(null, (_get, set) => {
  set(panelStateAtom, getInitialPanelState());
  set(materialReadCountAtom, {});
  set(resetActiveToolAtom);
});

export const changePanelDataAtom = atom(null, (get, set, material?: Material) => {
  const panelData = get(panelStateAtom).panelData;
  if (panelData?.entity.id === material?.entity.id) {
    return;
  }
  set(
    panelStateAtom,
    produce((draft) => {
      draft.panelData = material;
    }),
  );
  set(readMaterialAtom, material?.id);

  window.postMessage({ type: 'youmind_switch_board_item' }, '*');
});

export const updatePanelDataEntityAtom = atom(
  null,
  (_get, set, entity?: Partial<Material['entity']>) => {
    set(
      panelStateAtom,
      produce((draft) => {
        if (draft?.panelData?.entity) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          draft.panelData.entity = {
            ...draft.panelData.entity,
            ...entity,
          };
        }
      }),
    );
  },
);
export const winkFocusPanelAtom = atom<boolean>(false);

export const removeMaterialAtom = atom(null, (_get, set, materialId: string) => {
  set(clearPanelDataAtom);
  set(removeBoardItemAtom, materialId);
});

export const createBoardThoughtAtom = atom(
  null,
  async (_get, _sett, boardId: string, parentBoardGroupId?: string) => {
    const { data } = await callHTTP('/api/v1/createThought', {
      method: 'POST',
      body: {
        content: {
          raw: '',
        },
        board_id: boardId,
        parent_board_group_id: parentBoardGroupId,
      },
    });
    return data;
  },
);

export const createBoardThoughtAndFocusAtom = atom(
  null,
  async (_get, set, boardId: string, parentBoardGroupId?: string, parentId?: string) => {
    const data = await set(createBoardThoughtAtom, boardId, parentBoardGroupId);

    if (data) {
      const { board_item } = data;
      const thoughtBoardItem = {
        ...board_item!,
        parentId,
        parent_board_group_id: parentBoardGroupId,
        entity: data,
        entity_type: BoardItemTypeEnum.THOUGHT,
      };
      set(
        boardDetailAtom,
        produce((draft: Board | null) => {
          if (draft) {
            draft.items.unshift(thoughtBoardItem);
          }
        }),
      );
      set(changePanelDataAtom, thoughtBoardItem);
      return thoughtBoardItem;
    }
  },
);

export const useEnterBoardWorkSpace = () => {
  const boardDetail = useAtomValue(boardDetailAtom);
  const changePanelData = useSetAtom(changePanelDataAtom);
  const getBoardItemByEntityId = useAtomValue(getBoardItemByEntityIdAtom);
  const setNeedToFocusMaterial = useSetAtom(needToFocusMaterialAtom);
  const navigate = useNavigate();

  return (boardId: string, entityType?: BoardItemTypeEnum, entityId?: string) => {
    if (boardId === boardDetail?.id) {
      changePanelData(getBoardItemByEntityId(entityId!) ?? undefined);
      return;
    }

    if (entityType && entityId) {
      setNeedToFocusMaterial({ entity_type: entityType, entity_id: entityId });
      navigate(`/boards/${boardId}`);
    } else {
      navigate(`/boards/${boardId}`);
    }
  };
};

export const focusMaterialByEntityIdAtom = atom(null, (get, set, entityId: string) => {
  const boardDetail = get(boardDetailAtom);
  if (boardDetail) {
    const material = boardDetail.items.find((item) => item.entity.id === entityId);
    if (material) {
      set(changePanelDataAtom, material);
    }
  }
});
