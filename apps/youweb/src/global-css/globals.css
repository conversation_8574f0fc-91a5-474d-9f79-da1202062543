@import "../../../../packages/ui/src/styles/globals.css";
@import "../components/ai-ask/index.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  height: 100%;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}
@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}

@keyframes loading-shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 250% 0;
  }
}

@keyframes shine {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  to {
    background-position: 0% 0%;
  }
}

:root {
  .ant-image-preview-operations {
    background-color: rgba(0, 0, 0, 0.22) !important;
  }

  /* 图片预览白色背景 */
  .ant-image-preview-img {
    background-color: rgba(255, 255, 255, 1) !important;
  }

  --text-quaternary: #989798;
  --text-primary: #0d0d0d;

  .dark {
    /* --text-quaternary: #989798;
    --text-primary: #0d0d0d; */
  }
  /* 这里不能用 hsl，会丢失渐变效果 */
  /* --text-quaternary: hsl(var(--caption));
  --text-primary: hsl(var(--foreground)); */

  .loading-shimmer {
    -webkit-text-fill-color: transparent;
    animation-delay: 0s;
    animation-duration: 2.5s;
    animation-iteration-count: infinite;
    animation-name: loading-shimmer;
    background: var(--text-quaternary)
      -webkit-gradient(
        linear,
        100% 0,
        0 0,
        from(var(--text-quaternary)),
        color-stop(0.5, var(--text-primary)),
        to(var(--text-quaternary))
      );
    -webkit-background-clip: text;
    background-clip: text;
    background-repeat: no-repeat;
    background-size: 50% 200%;
    display: inline-block;
  }

  [dir="ltr"] .loading-shimmer {
    background-position: -100% 0;
  }

  [dir="rtl"] .loading-shimmer {
    background-position: 200% 0;
  }

  .loading-shimmer:hover {
    -webkit-text-fill-color: var(--text-primary);
    animation: none;
  }

  [dir="ltr"] .loading-shimmer:hover {
    background: 0 0;
  }

  [dir="rtl"] .loading-shimmer:hover {
    background: 100% 0;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground !important;
  }

  h1,
  .font-h1 {
    font-size: 4.25rem;
    line-height: 1.2;
    @apply font-medium;
  }

  h2,
  .font-h2 {
    font-size: 2.5rem;
    line-height: 1.2;
    @apply font-medium;
  }

  h3,
  .font-h3 {
    font-size: 2rem;
    line-height: 1.2;
    @apply font-medium;
  }

  h4,
  .font-h4 {
    font-size: 1.75rem;
    line-height: 1.4;
    @apply font-medium;
  }

  h5,
  .font-h5 {
    font-size: 1.5rem;
    line-height: 1.4;
    @apply font-medium;
  }

  h6,
  .font-h6,
  .font-title {
    font-size: 1.25rem;
    line-height: 1.5;
    @apply font-medium;
  }

  .font-subtitle {
    font-size: 1rem;
    line-height: 1.5;
    @apply font-medium;
  }

  .font-paragraph {
    font-size: 1rem;
    line-height: 1.5;
    @apply font-normal;
  }

  .font-body {
    font-size: 0.875rem;
    line-height: 1.25rem;
    @apply font-normal;
  }

  .font-caption {
    font-size: 0.75rem;
    line-height: 1.125rem;
    @apply font-normal;
    @apply text-caption;
  }

  .font-caption-s {
    font-size: 0.625rem;
    line-height: 0.875rem;
    @apply font-normal;
    @apply text-caption;
  }

  .inverted-img {
    filter: invert(1);
  }

  .dark .inverted-img {
    filter: invert(0);
  }

  .reversed-img {
    filter: invert(0);
  }

  .dark .reversed-img {
    filter: invert(1);
  }
}

@layer components {
  /**
   * Typography
   */
  .footnote {
    @apply text-xs;
    line-height: 18px;
    font-weight: 400;
  }

  .caption {
    @apply text-xs;
    line-height: 18px;
    font-weight: 400;
  }

  .body {
    @apply text-sm;
    line-height: 20px;
    font-weight: 400;
  }

  .body-bold {
    @apply text-sm;
    line-height: 20px;
    font-weight: 500;
  }

  .body-strong {
    @apply text-sm;
    line-height: 20px;
    font-weight: 500;
  }

  .paragraph {
    @apply text-base;
    line-height: 26px;
    font-weight: 400;
  }

  .title {
    @apply text-base;
    line-height: 24px;
    font-weight: 500;
  }

  .title-large {
    @apply text-lg;
    font-size: 20px;
    line-height: 28px;
    font-weight: 590;
  }

  .headline3 {
    @apply text-2xl;
    line-height: 32px;
    font-weight: 500;
  }

  .headline2 {
    @apply text-2xl;
    line-height: 36px;
    font-weight: 500;
  }

  .headline1 {
    @apply text-3xl;
    line-height: 40px;
    font-weight: 500;
  }

  .display {
    @apply text-4xl;
    line-height: 48px;
    font-weight: 500;
  }

  .display-medium {
    font-size: 44px;
    font-style: normal;
    font-weight: 590;
    line-height: 52px;
  }

  .display-large {
    @apply text-5xl;
    line-height: 80px;
    font-weight: 500;
  }

  .text-ellipsis-line-1 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .text-ellipsis-line-2 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .text-ellipsis-line-3 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .text-ellipsis-line-4 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }

  .overview-container {
    p {
      margin-bottom: 8px;
    }

    .ym-citation-btn {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      /* background-color: hsl(var(--muted-foreground)); */
      background: #02041a14;
      margin-left: 4px;
    }

    & p .ym-citation-btn {
      display: none;
    }

    & > p {
      color: hsl(var(--secondary-foreground));
    }

    h3 {
      font-size: 14px;
      font-weight: 700;
      line-height: 22px;
      margin-bottom: 16px;
      margin-top: 16px;
      color: hsl(var(--foreground));
    }

    div.sc {
      border-radius: 12px;
      /* background-color: hsl(var(--muted)); */
      /* padding: 12px 16px; */
      margin-bottom: 16px;
      color: hsl(var(--secondary-foreground));
    }

    h4 {
      font-size: 14px;
      font-weight: 400;
      color: hsl(var(--foreground));
      margin-bottom: 8px;
    }

    ul,
    ol {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      padding: 8px 24px;
      padding-top: 0px;
      padding-right: 0px;
      list-style: disc;
    }

    li {
      margin-left: 8px;
      padding-right: 16px;
    }
  }

  .block-media-grid-sizer,
  .block-media-grid-item {
    width: 33.3333%;
  }

  /* Hide scrollbar for Chrome, Safari, and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge, and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  * {
    outline: none !important;
  }

  .smaller-h {
    h1 {
      font-size: 1.6em;
      line-height: 1.25em;
    }

    h2 {
      font-size: 1.2em;
      line-height: 1.51em;
    }

    h3 {
      font-size: 1em;
      line-height: 1.66em;
    }
  }

  .text-snippet-card-content {
    h1 {
      font-size: 14px;
      line-height: 22px;
      margin: 8px 0;
      font-weight: 600;
    }

    h2 {
      font-size: 14px;
      line-height: 22px;
      margin: 8px 0;
      font-weight: 500;
    }

    h3 {
      font-size: 14px;
      line-height: 22px;
      margin: 8px 0;
    }

    p {
      margin: 6px 0;
    }
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  .theme {
    --duration: 8s;
    --animate-shine: shine var(--duration) infinite linear;
  }
}

/* 使用 CSS 变量 */
:root {
  --scrollbar-thumb: rgba(0, 0, 0, 0.1);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar {
  width: 4px; /* 修改滚动条宽度,默认是16px */
  height: 4px; /* 设置横向滚动条的高度 */
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}

/* Firefox */
* {
  scrollbar-color: var(--scrollbar-thumb) transparent;
}
