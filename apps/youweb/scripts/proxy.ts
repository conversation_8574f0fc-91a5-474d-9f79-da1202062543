export const proxyRoutes = ['/api', '/_next', '/sign-in', '/webhook'];

export const getDevServerProxyConfig = () => {
  const proxyToPreview = process.env.PROXY_TO === 'preview';

  const host = proxyToPreview ? 'youmind-preview.vercel.app' : 'localhost:3001';
  const apiHost = proxyToPreview ? 'youmind-preview.vercel.app' : 'localhost:4000';
  const protocol = proxyToPreview ? 'https' : 'http';
  const origin = `${protocol}://${host}`;
  const apiOrigin = `${protocol}://${apiHost}`;

  return [
    {
      context: ['/_next', '/sign-in', '/webhook'],
      target: origin,
      changeOrigin: true,
      headers: {
        'x-forwarded-host': host,
        'x-forwarded-proto': protocol,
        host,
        origin,
        'youmind-preview-origin': 'http://localhost:2000',
      },
    },
    {
      context: ['/api'],
      target: apiOrigin,
      changeOrigin: true,
      headers: {
        'x-forwarded-host': apiHost,
        'x-forwarded-proto': protocol,
        host: apiHost,
        origin: apiOrigin,
        'youmind-preview-origin': 'http://localhost:2000',
      },
    },
  ];
};
