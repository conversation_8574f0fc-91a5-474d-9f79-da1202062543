import injector from 'connect-injector';
import { proxyRoutes } from './proxy';

export const scriptInjector = (scriptsToInject: string[]) =>
  injector(
    (req, _res) => {
      // 跳过需要代理的路径，否则本地跳转 sign-in 会失效
      if (proxyRoutes.some((path) => req.url?.startsWith(path))) {
        return false;
      }
      return req.method === 'GET' && req.headers.accept?.includes('text/html');
    },
    (data, req, _res, callback) => {
      let responseData = data.toString();
      if (responseData.includes('</head>')) {
        const userData = (req as any).userData;

        // 注入用户数据和应用配置（仅客户端安全配置）
        const scripts = [
          `<script>window.YOUMIND_USER = ${JSON.stringify(userData)};</script>`,
          ...scriptsToInject,
        ].join('\n');

        responseData = responseData.replace('</head>', `${scripts}</head>`);
      }
      callback(null, responseData);
    },
  );

export const fetUserMiddleWare = async (req, _res, next) => {
  // 跳过需要代理的路径，否则本地跳转 sign-in 会失效
  if (proxyRoutes.some((path) => req.url?.startsWith(path))) {
    return next();
  }

  if (req.method === 'GET' && req.headers.accept?.includes('text/html')) {
    try {
      const proxyToPreview = process.env.PROXY_TO === 'preview';
      const host = proxyToPreview ? 'youmind-preview.vercel.app' : 'localhost:4000';
      const protocol = proxyToPreview ? 'https' : 'http';
      const origin = `${protocol}://${host}`;
      const userResponse = await fetch(`${origin}/api/v1/getCurrentUser`, {
        method: 'POST',
        headers: {
          cookie: req.headers.cookie || '',
          'content-type': 'application/json',
          accept: '*/*',
          'x-forwarded-host': host,
          'x-forwarded-proto': protocol,
          host,
          origin,
        },
      });

      const userData = await userResponse.json();

      // 将用户数据存储到 req 对象上，供后续中间件使用
      (req as any).userData = userData;

      return next();
    } catch (error) {
      console.error(`dev server 查询用户失败，请先访问 /sign-in 登录`, error);
      console.error(
        `/sign-in 将转发到 ${process.env.PROXY_TO === 'preview' ? '预发环境' : 'http://localhost:4000 环境'}`,
      );
      throw error;
    }
  }
};
