import fs from 'node:fs';
import path from 'node:path';
import dotenv from 'dotenv';
import { expand } from 'dotenv-expand';

/**
 * 按优先级加载环境文件并返回合并后的环境变量对象
 * @param workingDir 工作目录，默认为当前工作目录
 * @returns 合并后的环境变量对象
 */
export function getEnv(): Record<string, string> {
  // 检测是否在 GitHub Actions 环境中 - 使用我们控制的可靠指标
  // YOUWEB_ENV_CONFIG 只在我们的 deploy-youweb.yml 中设置，是最可靠的指标
  const isGitHubActions = !!process.env.YOUWEB_ENV_CONFIG;

  // 在构建部署时，读取 GitHub Action 中注入的 env
  if (isGitHubActions) {
    return getEnvFromGitHubAction();
  }

  // 确定环境
  const env = process.env.YOUMIND_ENV || 'preview';

  // 按优先级加载环境文件（高到低）
  const files = [`.env.${env}.local`, `.env.${env}`, '.env.local', '.env'];

  // 用于存储合并后的环境变量
  const mergedEnv: Record<string, string> = {};
  const loadedFiles: string[] = [];

  // 反向遍历文件（从低优先级到高优先级），后加载的会覆盖先加载的
  files.reverse().forEach((file) => {
    const filePath = path.resolve(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      // 读取文件内容
      const fileContent = fs.readFileSync(filePath, 'utf8');

      // 解析 .env 文件
      const parsed = dotenv.parse(fileContent);

      // 合并到结果对象中
      Object.assign(mergedEnv, parsed);
      loadedFiles.push(file);
    }
  });

  // 使用 dotenv-expand 处理变量引用
  const expandResult = expand({ parsed: mergedEnv });
  const finalEnv = expandResult.parsed || mergedEnv;

  if (!finalEnv.YOUMIND_ENV) {
    finalEnv.YOUMIND_ENV = env;
  }

  return finalEnv;
}

function getEnvFromGitHubAction(): Record<string, string> {
  // 从 GitHub Action 传入的 Doppler 配置 JSON 中读取环境变量
  const dopplerConfigJson = process.env.YOUWEB_ENV_CONFIG;

  if (!dopplerConfigJson) {
    console.warn('⚠️ YOUWEB_ENV_CONFIG not found in GitHub Actions environment');
    return {
      YOUMIND_ENV: process.env.YOUMIND_ENV || 'preview',
      NODE_ENV: 'production',
    };
  }

  try {
    // 解析 Doppler 输出的 JSON
    const dopplerOutputs = JSON.parse(dopplerConfigJson);

    // 过滤掉 Doppler 元数据字段，只保留实际的环境变量
    const envVars: Record<string, string> = {};

    for (const [key, value] of Object.entries(dopplerOutputs)) {
      if (key !== 'DOPPLER_PROJECT' && key !== 'DOPPLER_ENVIRONMENT' && key !== 'DOPPLER_CONFIG') {
        envVars[key] = String(value);
      }
    }

    // 确保核心环境变量存在
    envVars.YOUMIND_ENV = process.env.YOUMIND_ENV || 'preview';
    envVars.NODE_ENV = 'production';

    return envVars;
  } catch (error) {
    console.error('❌ Failed to parse YOUWEB_ENV_CONFIG:', error);
    return {
      YOUMIND_ENV: process.env.YOUMIND_ENV || 'preview',
      NODE_ENV: 'production',
    };
  }
}

// 默认导出
export default getEnv;
