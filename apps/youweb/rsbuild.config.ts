import path from 'node:path';
import { getFavicon } from '@repo/ui/lib/get-favicon';
import { defineConfig } from '@rsbuild/core';
import { pluginLess } from '@rsbuild/plugin-less';
import { pluginNodePolyfill } from '@rsbuild/plugin-node-polyfill';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSass } from '@rsbuild/plugin-sass';
import { pluginHtmlMinifierTerser } from 'rsbuild-plugin-html-minifier-terser';
import appConfig from './scripts/get-config';
import getEnv from './scripts/get-env';
import { scriptInjector } from './scripts/inject-user';
// import { fetUserMiddleWare, scriptInjector } from './scripts/inject-user';
import { getDevServerProxyConfig } from './scripts/proxy';

export default defineConfig({
  resolve: {
    alias: {
      '@components': './src/components',
      '@repo/common': '../../packages/common/src',
      '@repo/editor-common': '../../packages/editor-common/src',
      '@repo/ui-business-editor': '../../packages/ui-business-editor/src',
      '@repo/ui-business-snip': '../../packages/ui-business-snip/src',
      react: path.resolve('./node_modules/react'),
    },
  },
  tools: {
    rspack: {
      module: {
        rules: [
          {
            test: /\.md$/,
            type: 'asset/source',
          },
        ],
      },
    },
  },
  plugins: [
    pluginReact(),
    pluginLess(),
    pluginSass(),
    pluginHtmlMinifierTerser(),
    pluginNodePolyfill(),
  ],
  server: {
    publicDir: false,
    port: 2000,
    historyApiFallback: true,
    proxy: getDevServerProxyConfig(),
  },
  source: {
    define: {
      'process.env': JSON.stringify(getEnv()),
    },
  },
  dev: {
    setupMiddlewares: [
      (middlewares) => {
        middlewares.unshift(
          scriptInjector([
            `<script>window.__APP_CONFIG__ = ${JSON.stringify(appConfig)};</script>`,
          ]),
        );
        // middlewares.unshift(fetUserMiddleWare);
      },
    ],
  },
  html: {
    template: './scripts/index.html',
    ...calcHTMLConfig(),
  },
  output: {
    copy: [
      // `./src/assets/image.png` -> `./static/assets/image.png`
      { from: './public/assets', to: 'static/assets' },
    ],
  },
});

function calcHTMLConfig() {
  if (process.env.NODE_ENV === 'development') {
    return {
      // 统一获取 favicon 方法
      favicon: getFavicon('development'),
    };
  }
  if (process.env.YOUMIND_ENV === 'preview') {
    return {
      tags: [
        // 结果: <script>console.log("Hello, world!");</script>
        {
          tag: 'link',
          attrs: {
            rel: 'icon',
            href: getFavicon('preview'),
          },
        },
      ],
    };
  }
  return {
    tags: [
      // 结果: <script>console.log("Hello, world!");</script>
      {
        tag: 'link',
        attrs: {
          rel: 'icon',
          href: getFavicon('production'),
        },
      },
    ],
  };
}
