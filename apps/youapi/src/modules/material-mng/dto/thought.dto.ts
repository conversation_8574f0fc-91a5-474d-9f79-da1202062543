import { ApiProperty } from '@nestjs/swagger';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import type { BoardItemInfo, BoardPositionInfo } from '../domain/shared/board-position.service';
import { TitleType } from '../domain/thought/models/thought.entity';
import { ThoughtContentDto } from './thought-content.dto';

export class AbstractEntityDto {}

export class ThoughtDto extends SwaggerBaseDto {
  @ApiProperty({
    description: '想法ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: '空间ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  spaceId: string;

  @ApiProperty({
    description: '创建者ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  creatorId: string;

  @ApiProperty({
    description: '创作板ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  boardId?: string;

  @ApiProperty({
    description: '标题',
    example: 'My thought title',
  })
  title: string;

  @ApiProperty({
    description: '标题类型',
    enum: TitleType,
    enumName: 'TitleType',
    example: 'manual',
  })
  titleType: TitleType;

  @ApiProperty({
    description: '内容',
    type: ThoughtContentDto,
  })
  content?: ThoughtContentDto;

  @ApiProperty({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
    example: 'private',
  })
  visibility: Visibility;

  @ApiProperty({
    description: '位置信息',
    required: true,
  })
  position: BoardPositionInfo;

  /** @deprecated 请使用 position 字段替代 boardItem */
  @ApiProperty({
    description: '创作板关联信息（已废弃，请使用 position 字段）',
    required: false,
    deprecated: true,
  })
  boardItem?: BoardItemInfo;

  /** @deprecated 请使用 position 字段替代 boardItem */
  @ApiProperty({
    description: '创作板关联信息（已废弃，请使用 position 字段）',
    required: false,
    deprecated: true,
  })
  boardIds?: string[];

  @ApiProperty({
    description:
      '类型。目前为 swagger 生成 spec 所使用，目的在于区分不同类型的实体，没有实际业务作用',
    example: 'thought',
  })
  readonly type: string = 'thought';
}
