/**
 * Other Webpage DTO - 其他网页 DTO
 * 从 youapp 迁移的其他网页 DTO
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/types.ts (OtherWebpageVOSchema)
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { BoardItemDto } from './board-item.dto';
import { WebpageMetaDto } from './webpage-meta.dto';

export class OtherWebpageDto extends SwaggerBaseDto {
  @ApiProperty({
    description: '唯一标识符',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({ enum: SnipType, description: '类型', enumName: 'SnipType' })
  @IsEnum(SnipType)
  type: SnipType;

  @ApiProperty({ enum: SnipFrom, description: '来源分类', enumName: 'SnipFrom' })
  @IsEnum(SnipFrom)
  from: SnipFrom;

  @ApiProperty({
    description: '标题',
    example: '网页标题',
    maxLength: 2048,
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: '网页元信息',
    type: WebpageMetaDto,
  })
  @ValidateNested()
  @Type(() => WebpageMetaDto)
  webpage: WebpageMetaDto;

  @ApiProperty({
    description: '额外数据',
    example: '{"custom": "data"}',
    required: false,
  })
  @IsOptional()
  @IsString()
  extra?: string;

  @ApiProperty({
    description: '关联的创作板 ID 列表（已废弃，仅为兼容性保留）',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-426614174000'],
  })
  boardIds: string[];

  @ApiPropertyOptional({ enum: SnipStatus, description: '状态', enumName: 'SnipStatus' })
  @IsOptional()
  @IsEnum(SnipStatus)
  status?: SnipStatus;

  @ApiProperty({
    description: '看板项目信息',
    type: BoardItemDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BoardItemDto)
  boardItem?: BoardItemDto;

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
  })
  @IsOptional()
  visibility?: Visibility;
}
