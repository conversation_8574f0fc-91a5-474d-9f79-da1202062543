/**
 * Unknown Webpage DTO - 未知网页数据传输对象
 * 从 youapp 迁移的未知网页DTO
 *
 * Migrated from:
 * - /youapp/src/app/_api/types/unknown-webpage.vo.ts
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { WebpageMetaDto } from './webpage-meta.dto';

/**
 * Board Item DTO - 板块关联信息
 */
export class BoardItemDto {
  @ApiProperty({ description: 'Board Item ID' })
  id: string;

  @ApiProperty({ description: 'Board ID' })
  boardId: string;

  @ApiPropertyOptional({ description: 'Parent Board Group ID' })
  parentBoardGroupId?: string;

  @ApiPropertyOptional({ description: 'Board Group ID' })
  boardGroupId?: string;

  @ApiProperty({ description: 'Rank' })
  rank: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class UnknownWebpageDto extends SwaggerBaseDto {
  @ApiProperty({ description: '资源ID' })
  id: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '类型', enum: SnipType, enumName: 'SnipType' })
  type: SnipType;

  @ApiProperty({ description: '来源', enum: SnipFrom, enumName: 'SnipFrom' })
  from: SnipFrom;

  @ApiProperty({ description: '标题', required: false })
  title?: string;

  @ApiProperty({
    description: '网页元数据',
    type: WebpageMetaDto,
  })
  webpage: WebpageMetaDto;

  @ApiProperty({ description: '额外信息', required: false })
  extra?: string;

  @ApiProperty({ description: '状态', enum: SnipStatus, enumName: 'SnipStatus' })
  status: SnipStatus;

  @ApiProperty({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
  })
  visibility: Visibility;

  @ApiProperty({
    description: '所属看板ID列表',
    type: [String],
    example: ['board-123'],
  })
  boardIds: string[];

  @ApiProperty({
    description: '看板项信息',
    type: BoardItemDto,
    required: false,
  })
  boardItem?: BoardItemDto;
}
