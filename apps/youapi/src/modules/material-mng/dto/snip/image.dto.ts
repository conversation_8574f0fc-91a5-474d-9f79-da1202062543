/**
 * Image DTO - 图片数据传输对象
 * 用于 API 响应的图片数据结构
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/types.ts (ImageVOSchema)
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { WebpageMeta } from '../../domain/snip/value-objects/webpage-meta.vo';

/**
 * 文件元信息 DTO - 支持多种文件类型
 */
export class FileMetaDto {
  @ApiProperty({ description: '文件名称', maxLength: 2048 })
  name: string;

  @ApiProperty({ description: '文件 MIME 类型', maxLength: 255 })
  mimeType: string;

  @ApiProperty({ description: '文件大小（字节）' })
  size: number;

  @ApiProperty({ description: '存储地址', maxLength: 2048 })
  storageUrl: string;

  @ApiPropertyOptional({ description: 'CDN 访问地址', maxLength: 2048 })
  url?: string;

  @ApiPropertyOptional({ description: '原始访问地址', maxLength: 2048 })
  originalUrl?: string;
}

/**
 * Board Item DTO - 创作板项目信息
 */
export class BoardItemDto {
  @ApiProperty({ description: '唯一标识' })
  id: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: 'Board 标识' })
  boardId: string;

  @ApiPropertyOptional({ description: '所属的 Board Group 标识' })
  parentBoardGroupId?: string;

  @ApiProperty({ description: '排序' })
  rank: string;
}

/**
 * Image DTO - 图片 DTO
 * 对应 youapp 的 ImageVOSchema
 */
export class ImageDto extends SwaggerBaseDto {
  @ApiProperty({ description: '唯一标识' })
  id: string;

  @ApiPropertyOptional({ description: '图片标题', maxLength: 2048 })
  title?: string;

  @ApiPropertyOptional({ description: '图片描述' })
  description?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({
    enum: SnipType,
    description: '类型',
    enumName: 'SnipType',
    default: SnipType.IMAGE,
  })
  type: SnipType;

  @ApiProperty({ enum: SnipFrom, description: '来源分类', enumName: 'SnipFrom' })
  from: SnipFrom;

  @ApiPropertyOptional({ description: '额外信息' })
  extra?: string;

  @ApiProperty({
    description: '创作板标识数组',
    type: [String],
    deprecated: true,
  })
  boardIds: string[];

  @ApiPropertyOptional({ enum: SnipStatus, description: '状态', enumName: 'SnipStatus' })
  status?: SnipStatus;

  @ApiPropertyOptional({ description: '创作板关联信息', type: BoardItemDto })
  boardItem?: BoardItemDto;

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
  })
  visibility?: Visibility;

  @ApiProperty({ description: '文件元信息', type: FileMetaDto })
  file: FileMetaDto;

  @ApiPropertyOptional({ description: '网页元信息', type: WebpageMeta })
  webpage?: WebpageMeta;

  @ApiProperty({ description: '空间 ID' })
  spaceId: string;

  @ApiProperty({ description: '创建者 ID' })
  creatorId: string;
}
