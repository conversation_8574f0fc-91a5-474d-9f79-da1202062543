/**
 * PDF DTO - PDF文档响应 DTO
 * 从 youapp 迁移的PDF文档响应 DTO
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { SnipContentDto } from '../snip-content.dto';
import { AuthorDto } from './author.dto';
import { WebpageMetaDto } from './webpage-meta.dto';

/**
 * Board Item DTO - 板块关联信息
 */
export class BoardItemDto {
  @ApiProperty({ description: 'Board Item ID' })
  id: string;

  @ApiProperty({ description: 'Board ID' })
  boardId: string;

  @ApiPropertyOptional({ description: 'Parent Board Group ID' })
  parentBoardGroupId?: string;

  @ApiPropertyOptional({ description: 'Board Group ID' })
  boardGroupId?: string;

  @ApiProperty({ description: 'Rank' })
  rank: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class PdfDto extends SwaggerBaseDto {
  @ApiProperty({
    description: 'PDF文档 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty({
    description: '空间 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  spaceId: string;

  @ApiProperty({
    description: '创建者 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  creatorId: string;

  @ApiProperty({
    description: 'PDF文档标题',
    example: '技术白皮书',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ enum: SnipType, description: '类型', enumName: 'SnipType' })
  @IsOptional()
  @IsEnum(SnipType)
  type?: SnipType;

  @ApiPropertyOptional({ enum: SnipFrom, description: '来源分类', enumName: 'SnipFrom' })
  @IsOptional()
  @IsEnum(SnipFrom)
  from?: SnipFrom;

  @ApiPropertyOptional({ enum: SnipStatus, description: '状态', enumName: 'SnipStatus' })
  @IsOptional()
  @IsEnum(SnipStatus)
  status?: SnipStatus;

  @ApiPropertyOptional({
    description: '父级ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  parentId?: string;

  @ApiPropertyOptional({
    description: '板块ID列表',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  boardIds?: string[];

  @ApiProperty({
    description: '解析后的PDF内容',
    type: SnipContentDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SnipContentDto)
  content?: SnipContentDto;

  @ApiProperty({
    description: '网页元信息（URL创建时）',
    type: WebpageMetaDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WebpageMetaDto)
  webpage?: WebpageMetaDto;

  @ApiProperty({
    description: 'PDF作者列表',
    type: [AuthorDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AuthorDto)
  authors?: AuthorDto[];

  @ApiProperty({
    description: '封面图片 URL',
    example: 'https://example.com/cover.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  heroImageUrl?: string;

  @ApiProperty({
    description: '发布时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  publishedAt?: Date;

  @ApiProperty({
    description: '内容格式',
    example: 'reader_html',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentFormat?: string;

  @ApiProperty({
    description: '原始内容',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentRaw?: string;

  @ApiProperty({
    description: '纯文本内容',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentPlain?: string;

  @ApiProperty({
    description: '内容语言',
    example: 'zh',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentLanguage?: string;

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
  })
  @IsOptional()
  visibility?: Visibility;

  @ApiProperty({
    description: '创作板项目信息',
    type: BoardItemDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BoardItemDto)
  boardItem?: BoardItemDto;

  @ApiProperty({
    description: '额外数据',
    required: false,
  })
  @IsOptional()
  @IsObject()
  extra?: any;

  @ApiPropertyOptional({
    description: '文件信息',
    type: 'object',
    properties: {
      name: { type: 'string', description: '文件名' },
      mimeType: { type: 'string', description: 'MIME类型' },
      size: { type: 'number', description: '文件大小' },
      storageUrl: { type: 'string', description: '存储URL' },
      url: { type: 'string', description: 'CDN访问URL' },
      originalUrl: { type: 'string', description: '原始URL' },
    },
  })
  @IsOptional()
  @IsObject()
  file?: {
    name: string;
    mimeType: string;
    size: number;
    storageUrl: string;
    url?: string;
    originalUrl?: string;
  };
}
