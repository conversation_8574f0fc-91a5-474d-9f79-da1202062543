/**
 * Office DTO - Office文档响应 DTO
 * 从 youapp 迁移的Office文档响应 DTO
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { SnipContentDto } from '../snip-content.dto';
import { AuthorDto } from './author.dto';

/**
 * Office 文件元信息 DTO
 */
export class OfficeFileDto {
  @ApiProperty({ description: '文件名称', example: 'document.docx' })
  @IsString()
  name: string;

  @ApiProperty({
    description: '文件MIME类型',
    example: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  })
  @IsString()
  mimeType: string;

  @ApiProperty({ description: '文件大小（字节）', example: 1024000 })
  @IsNumber()
  size: number;

  @ApiProperty({ description: '文件存储URL', example: 'https://storage.example.com/document.docx' })
  @IsUrl()
  storageUrl: string;

  @ApiPropertyOptional({
    description: 'CDN 访问地址',
    example: 'https://cdn.gooo.ai/user-files/hash@large',
  })
  @IsOptional()
  @IsUrl()
  url?: string;

  @ApiPropertyOptional({ description: '原始访问地址' })
  @IsOptional()
  @IsUrl()
  originalUrl?: string;
}

/**
 * Board Item DTO - 板块关联信息
 */
export class BoardItemDto {
  @ApiProperty({ description: 'Board Item ID' })
  id: string;

  @ApiProperty({ description: 'Board ID' })
  boardId: string;

  @ApiPropertyOptional({ description: 'Parent Board Group ID' })
  parentBoardGroupId?: string;

  @ApiPropertyOptional({ description: 'Board Group ID' })
  boardGroupId?: string;

  @ApiProperty({ description: 'Rank' })
  rank: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class OfficeDto extends SwaggerBaseDto {
  @ApiProperty({
    description: 'Office文档 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty({
    description: '空间 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  spaceId: string;

  @ApiProperty({
    description: '创建者 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  creatorId: string;

  @ApiProperty({
    description: 'Office文档标题',
    example: '项目计划书',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: '解析后的文档内容',
    type: SnipContentDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SnipContentDto)
  content?: SnipContentDto;

  @ApiProperty({
    description: '封面图片 URL',
    example: 'https://example.com/cover.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  heroImageUrl?: string;

  @ApiProperty({
    description: '文档作者列表',
    type: [AuthorDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AuthorDto)
  authors?: AuthorDto[];

  @ApiProperty({
    description: '发布时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  publishedAt?: Date;

  @ApiProperty({
    description: '内容格式',
    example: 'reader_html',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentFormat?: string;

  @ApiProperty({
    description: '原始内容',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentRaw?: string;

  @ApiProperty({
    description: '纯文本内容',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentPlain?: string;

  @ApiProperty({
    description: '内容语言',
    example: 'zh',
    required: false,
  })
  @IsOptional()
  @IsString()
  contentLanguage?: string;

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
    example: 'private',
  })
  @IsOptional()
  @IsString()
  visibility?: Visibility;

  @ApiProperty({
    description: '创作板项目信息',
    type: BoardItemDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BoardItemDto)
  boardItem?: BoardItemDto;

  @ApiProperty({
    description: '额外数据',
    required: false,
  })
  @IsOptional()
  @IsObject()
  extra?: any;

  @ApiProperty({
    description: '文件信息',
    type: OfficeFileDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OfficeFileDto)
  file?: OfficeFileDto;

  @ApiProperty({
    description: '类型',
    enum: SnipType,
    enumName: 'SnipType',
    example: SnipType.OFFICE,
  })
  @IsEnum(SnipType)
  type: SnipType;

  @ApiProperty({
    description: '来源',
    enum: SnipFrom,
    enumName: 'SnipFrom',
    example: SnipFrom.FILE,
  })
  @IsEnum(SnipFrom)
  from: SnipFrom;

  @ApiPropertyOptional({
    description: '状态',
    enum: SnipStatus,
    enumName: 'SnipStatus',
    example: SnipStatus.FETCHING,
  })
  @IsOptional()
  @IsEnum(SnipStatus)
  status?: SnipStatus;

  @ApiProperty({
    description: '板块ID列表',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  boardIds?: string[];
}
