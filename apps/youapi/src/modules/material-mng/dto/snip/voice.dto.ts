/**
 * Voice DTO - 语音响应 DTO
 * 从 youapp 迁移的语音响应 DTO
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { SnipContentDto } from '../snip-content.dto';
import { AuthorDto } from './author.dto';
import { WebpageMetaDto } from './webpage-meta.dto';

/**
 * Board Item DTO - 板块关联信息
 */
export class BoardItemDto {
  @ApiProperty({ description: 'Board Item ID' })
  id: string;

  @ApiProperty({ description: 'Board ID' })
  boardId: string;

  @ApiPropertyOptional({ description: 'Parent Board Group ID' })
  parentBoardGroupId?: string;

  @ApiPropertyOptional({ description: 'Board Group ID' })
  boardGroupId?: string;

  @ApiProperty({ description: 'Rank' })
  rank: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class VoiceDto extends SwaggerBaseDto {
  @ApiProperty({
    description: '语音 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty({
    description: '空间 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  spaceId: string;

  @ApiProperty({
    description: '创建者 ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  creatorId: string;

  @ApiProperty({
    description: '类型',
    enum: SnipType,
    enumName: 'SnipType',
    example: SnipType.VOICE,
  })
  @IsEnum(SnipType)
  type: SnipType;

  @ApiProperty({
    description: '来源',
    enum: SnipFrom,
    enumName: 'SnipFrom',
    example: SnipFrom.WEBPAGE,
  })
  @IsEnum(SnipFrom)
  from: SnipFrom;

  @ApiPropertyOptional({
    description: '状态',
    enum: SnipStatus,
    enumName: 'SnipStatus',
    example: SnipStatus.FETCHING,
  })
  @IsOptional()
  @IsEnum(SnipStatus)
  status?: SnipStatus;

  @ApiProperty({
    description: '语音标题',
    example: '项目会议录音',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: '网页元信息',
    type: WebpageMetaDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WebpageMetaDto)
  webpage?: WebpageMetaDto;

  @ApiProperty({
    description: '节目说明',
    type: SnipContentDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SnipContentDto)
  showNotes?: SnipContentDto;

  @ApiProperty({
    description: '语音作者列表',
    type: [AuthorDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AuthorDto)
  authors?: AuthorDto[];

  @ApiProperty({
    description: '封面图片 URL',
    example: 'https://example.com/cover.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  heroImageUrl?: string;

  @ApiProperty({
    description: '发布时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  publishedAt?: Date;

  @ApiProperty({
    description: '播放 URL',
    example: 'https://example.com/audio.mp3',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  playUrl?: string;

  @ApiPropertyOptional({
    description: '文件信息',
    type: 'object',
    properties: {
      name: { type: 'string', description: '文件名' },
      mimeType: { type: 'string', description: 'MIME类型' },
      size: { type: 'number', description: '文件大小' },
      storageUrl: { type: 'string', description: '存储URL' },
      url: { type: 'string', description: 'CDN访问URL' },
      originalUrl: { type: 'string', description: '原始URL' },
    },
  })
  @IsOptional()
  @IsObject()
  file?: {
    name: string;
    mimeType: string;
    size: number;
    storageUrl: string;
    url?: string;
    originalUrl?: string;
  };

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
    example: 'private',
  })
  @IsOptional()
  @IsString()
  visibility?: Visibility;

  @ApiProperty({
    description: '创作板项目信息',
    type: BoardItemDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BoardItemDto)
  boardItem?: BoardItemDto;

  @ApiProperty({
    description: '额外数据',
    required: false,
  })
  @IsOptional()
  @IsObject()
  extra?: any;

  @ApiPropertyOptional({
    description: '板块ID列表',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  boardIds?: string[];
}
