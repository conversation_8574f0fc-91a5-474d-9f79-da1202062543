/**
 * Snippet DTO - 网页片段数据传输对象
 * 用于 API 响应的网页片段数据结构
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/types.ts (SnippetVOSchema)
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { WebpageMeta } from '../../domain/snip/value-objects/webpage-meta.vo';
import { SnipContentDto } from '../snip-content.dto';
import { BoardItemDto } from './article.dto';
import { HtmlSelectionDto } from './html-selection.dto';
import { RichtextContentDto } from './richtext-content.dto';

/**
 * Snippet DTO - 网页片段 DTO
 * 对应 youapp 的 SnippetVOSchema
 */
export class SnippetDto extends SwaggerBaseDto {
  @ApiProperty({ description: '唯一标识' })
  id: string;

  @ApiProperty({ description: '标题', maxLength: 2048 })
  title: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ enum: SnipType, description: '类型', enumName: 'SnipType' })
  type: SnipType;

  @ApiProperty({ enum: SnipFrom, description: '来源分类', enumName: 'SnipFrom' })
  from: SnipFrom;

  @ApiPropertyOptional({ description: '额外信息' })
  extra?: string;

  @ApiProperty({
    description: '创作板标识数组',
    type: [String],
    deprecated: true,
  })
  boardIds: string[];

  @ApiPropertyOptional({ enum: SnipStatus, description: '状态', enumName: 'SnipStatus' })
  status?: SnipStatus;

  @ApiPropertyOptional({ description: '创作板关联信息', type: BoardItemDto })
  boardItem?: BoardItemDto;

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
  })
  visibility?: Visibility;

  @ApiPropertyOptional({ description: '父Snip标识' })
  parentId?: string;

  @ApiProperty({ description: '网页元信息', type: WebpageMeta })
  webpage: WebpageMeta;

  @ApiProperty({ description: '短文内容', type: SnipContentDto })
  content: SnipContentDto;

  @ApiProperty({ description: '选区信息', type: HtmlSelectionDto })
  selection: HtmlSelectionDto;

  @ApiPropertyOptional({ description: '富文本批注', type: RichtextContentDto })
  annotation?: RichtextContentDto;

  @ApiProperty({ description: '空间 ID' })
  spaceId: string;

  @ApiProperty({ description: '创建者 ID' })
  creatorId: string;

  @ApiPropertyOptional({ description: 'Board ID' })
  boardId?: string;
}
