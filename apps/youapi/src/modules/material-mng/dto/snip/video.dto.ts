/**
 * Video DTO - 视频数据传输对象
 * 用于 API 响应的视频数据结构
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/types.ts (VideoVOSchema)
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import type { Author } from '@/common/types/snip.types';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { WebpageMeta } from '../../domain/snip/value-objects/webpage-meta.vo';
import { SnipContentDto } from '../snip-content.dto';

/**
 * Board Item DTO - 板块关联信息
 */
export class BoardItemDto {
  @ApiProperty({ description: 'Board Item ID' })
  id: string;

  @ApiProperty({ description: 'Board ID' })
  boardId: string;

  @ApiPropertyOptional({ description: 'Parent Board Group ID' })
  parentBoardGroupId?: string;

  @ApiPropertyOptional({ description: 'Board Group ID' })
  boardGroupId?: string;

  @ApiProperty({ description: 'Rank' })
  rank: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * Video DTO - 视频 DTO
 * 对应 youapp 的 VideoVOSchema
 */
export class VideoDto extends SwaggerBaseDto {
  @ApiProperty({ description: '唯一标识' })
  id: string;

  @ApiProperty({ description: '标题', maxLength: 2048 })
  title: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ enum: SnipType, description: '类型', enumName: 'SnipType' })
  type: SnipType;

  @ApiProperty({ enum: SnipFrom, description: '来源分类', enumName: 'SnipFrom' })
  from: SnipFrom;

  @ApiPropertyOptional({ description: '额外信息' })
  extra?: string;

  @ApiProperty({
    description: '创作板标识数组',
    type: [String],
    deprecated: true,
  })
  boardIds: string[];

  @ApiPropertyOptional({ enum: SnipStatus, description: '状态', enumName: 'SnipStatus' })
  status?: SnipStatus;

  @ApiPropertyOptional({ description: '创作板关联信息', type: BoardItemDto })
  boardItem?: BoardItemDto;

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
  })
  visibility?: Visibility;

  // Video 特有字段
  @ApiPropertyOptional({ description: '播放地址', maxLength: 2048 })
  playUrl?: string;

  @ApiProperty({ description: '网页元信息', type: WebpageMeta })
  webpage: WebpageMeta;

  @ApiProperty({ description: '视频描述', type: SnipContentDto })
  description: SnipContentDto;

  @ApiPropertyOptional({ description: '作者' })
  authors?: Author[];

  @ApiPropertyOptional({ description: '头图', maxLength: 2048 })
  heroImageUrl?: string;

  @ApiPropertyOptional({ description: '发布时间' })
  publishedAt?: Date;

  @ApiProperty({ description: '空间 ID' })
  spaceId: string;

  @ApiProperty({ description: '创建者 ID' })
  creatorId: string;

  @ApiPropertyOptional({ description: 'Board ID' })
  boardId?: string;
}
