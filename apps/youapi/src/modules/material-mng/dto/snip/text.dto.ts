/**
 * Text DTO - 文本文档数据传输对象
 * 用于 API 响应的文本文档数据结构
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/types.ts (TextVOSchema)
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { Visibility } from '@/shared/db/public.schema';
import { SnipFrom, SnipStatus, SnipType } from '../../domain/snip/models/snip.entity';
import { SnipContentDto } from '../snip-content.dto';
import { BoardItemDto } from './board-item.dto';

/**
 * File Meta DTO - 文件元信息
 */
export class FileMetaDto {
  @ApiProperty({ description: '文件名' })
  name: string;

  @ApiProperty({ description: '文件大小' })
  size: number;

  @ApiProperty({ description: 'MIME 类型' })
  mimeType: string;

  @ApiProperty({ description: '存储 URL' })
  storageUrl: string;

  @ApiPropertyOptional({ description: 'CDN 访问 URL' })
  url?: string;

  @ApiPropertyOptional({ description: '原始 URL' })
  originalUrl?: string;
}

/**
 * Text DTO - 文本文档 DTO
 * 对应 youapp 的 TextVOSchema
 */
export class TextDto extends SwaggerBaseDto {
  @ApiProperty({ description: 'Snip ID' })
  id: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '空间 ID' })
  spaceId: string;

  @ApiProperty({ description: '创建者 ID' })
  creatorId: string;

  @ApiProperty({
    description: 'Snip 类型',
    enum: SnipType,
    enumName: 'SnipType',
    example: SnipType.TEXT_FILE,
  })
  type: SnipType;

  @ApiProperty({
    description: 'Snip 来源',
    enum: SnipFrom,
    enumName: 'SnipFrom',
    example: SnipFrom.FILE,
  })
  from: SnipFrom;

  @ApiPropertyOptional({
    description: 'Snip 状态',
    enum: SnipStatus,
    enumName: 'SnipStatus',
  })
  status?: SnipStatus;

  @ApiPropertyOptional({ description: '标题' })
  title?: string;

  @ApiProperty({
    description: '内容',
    type: SnipContentDto,
  })
  content: SnipContentDto;

  @ApiProperty({
    description: '文件元信息',
    type: FileMetaDto,
  })
  file: FileMetaDto;

  @ApiPropertyOptional({ description: '额外数据' })
  extra?: string;

  @ApiPropertyOptional({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
  })
  visibility?: Visibility;

  @ApiPropertyOptional({
    description: '关联的板块 IDs',
    type: [String],
  })
  boardIds?: string[];

  @ApiPropertyOptional({
    description: '板块关联信息',
    type: BoardItemDto,
  })
  boardItem?: BoardItemDto;
}
