import { ApiProperty } from '@nestjs/swagger';
import { SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import { BoardGroupType } from '../domain/board-group/models/board-group.entity';
import { IconDto } from './shared/icon.dto';

export class BoardGroupDto extends SwaggerBaseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  creatorId: string;

  @ApiProperty()
  boardId: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ type: IconDto })
  icon: IconDto;

  @ApiProperty({
    description: 'Board group type',
    enum: BoardGroupType,
    enumName: 'BoardGroupType',
  })
  type: BoardGroupType;

  @ApiProperty({ required: false })
  boardItem?: any; // TODO: 应该定义具体的 BoardItem DTO 类型
}
