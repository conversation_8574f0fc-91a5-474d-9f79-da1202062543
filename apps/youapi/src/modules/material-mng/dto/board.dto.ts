import { ApiProperty, ApiPropertyOptional, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDefined,
  IsIn,
  IsNotEmptyObject,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';
import { DISCRIMINATOR_DEFAULT_NAME } from '@/common/base/swagger-base-dto';
import { BoardStatus, BoardType } from '../domain/board/models/board.entity';
import { BoardItemType } from '../domain/board-item/models/board-item.entity';
import { BoardGroupDto } from './board-group.dto';
import { IconDto } from './shared/icon.dto';
import { ArticleDto } from './snip/article.dto';
import { ImageDto } from './snip/image.dto';
import { OfficeDto } from './snip/office.dto';
import { OtherWebpageDto } from './snip/other-webpage.dto';
import { PdfDto } from './snip/pdf.dto';
import { SnipDto } from './snip/snip.dto';
import { SnippetDto } from './snip/snippet.dto';
import { TextDto } from './snip/text.dto';
import { UnknownWebpageDto } from './snip/unknown-webpage.dto';
import { VideoDto } from './snip/video.dto';
import { VoiceDto } from './snip/voice.dto';
import { ThoughtDto } from './thought.dto';

export class BoardDto {
  @ApiProperty({
    description: 'Board ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2023-10-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-10-01T10:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Board名称',
    example: 'My Board',
  })
  name: string;

  @ApiProperty({
    description: 'Board描述',
    example: 'This is my board description',
  })
  description: string;

  @ApiProperty({
    description: 'Board图标',
    type: IconDto,
  })
  icon: IconDto;

  @ApiPropertyOptional({
    description: '置顶时间',
    example: '2023-10-01T10:00:00.000Z',
  })
  pinnedAt?: Date;

  @ApiProperty({
    description: 'Board状态',
    example: 'in-progress',
  })
  status: BoardStatus;

  @ApiPropertyOptional({
    description: '头图URL列表',
    example: ['https://example.com/image1.jpg'],
  })
  heroImageUrls?: string[];

  @ApiPropertyOptional({
    description: 'Board简介',
    example: 'This is a detailed introduction',
  })
  intro?: string;

  @ApiProperty({
    description: 'Board类型',
    example: 'normal',
    enum: BoardType,
    enumName: 'BoardType',
  })
  type: BoardType;
}

export class BoardWithCountDto extends BoardDto {
  @ApiProperty({
    description: 'Snip数量',
    example: 10,
  })
  snipsCount: number;

  @ApiProperty({
    description: 'Thought数量',
    example: 5,
  })
  thoughtsCount: number;
}

export class BoardItemDto {
  @ApiProperty({
    description: 'Board Item ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2023-10-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-10-01T10:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Board ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  boardId: string;

  @ApiPropertyOptional({
    description: 'Parent Board Group ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  parentBoardGroupId?: string;

  @ApiProperty({
    description: 'Rank',
    example: 'a0',
  })
  rank: string;

  @ApiPropertyOptional({
    description: 'Snip ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  snipId?: string;

  @ApiPropertyOptional({
    description: 'Thought ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  thoughtId?: string;

  @ApiPropertyOptional({
    description: 'Board Group ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  boardGroupId?: string;

  @ApiPropertyOptional({
    description: 'Chat ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  chatId?: string;

  @ApiProperty({
    description: 'Entity Type',
    example: 'snip',
    enum: BoardItemType,
    enumName: 'BoardItemType',
  })
  entityType: BoardItemType;

  @ApiProperty({
    description: 'Entity Data',
    example: {},
    oneOf: [
      { $ref: getSchemaPath(ThoughtDto) },
      { $ref: getSchemaPath(SnippetDto) },
      { $ref: getSchemaPath(ArticleDto) },
      { $ref: getSchemaPath(OfficeDto) },
      { $ref: getSchemaPath(ImageDto) },
      { $ref: getSchemaPath(VideoDto) },
      { $ref: getSchemaPath(OtherWebpageDto) },
      { $ref: getSchemaPath(PdfDto) },
      { $ref: getSchemaPath(TextDto) },
      { $ref: getSchemaPath(UnknownWebpageDto) },
      { $ref: getSchemaPath(VoiceDto) },
      { $ref: getSchemaPath(BoardGroupDto) },
    ],
    discriminator: {
      propertyName: DISCRIMINATOR_DEFAULT_NAME,
      mapping: {
        ThoughtDto: getSchemaPath(ThoughtDto),
        SnippetDto: getSchemaPath(SnippetDto),
        ArticleDto: getSchemaPath(ArticleDto),
        ImageDto: getSchemaPath(ImageDto),
        VoiceDto: getSchemaPath(VoiceDto),
        VideoDto: getSchemaPath(VideoDto),
        PdfDto: getSchemaPath(PdfDto),
        OfficeDto: getSchemaPath(OfficeDto),
        TextDto: getSchemaPath(TextDto),
        OtherWebpageDto: getSchemaPath(OtherWebpageDto),
        UnknownWebpageDto: getSchemaPath(UnknownWebpageDto),
        BoardGroupDto: getSchemaPath(BoardGroupDto),
      },
    },
  })
  entity: ThoughtDto | SnipDto | BoardGroupDto;
}

export class BoardWithSomeBoardItemsDto extends BoardDto {
  @ApiProperty({
    description: 'Items数量',
    example: 15,
  })
  itemsCount: number;

  @ApiProperty({
    description: 'Board Items',
    type: [BoardItemDto],
  })
  items: BoardItemDto[];
}

// Request DTOs
export class CreateBoardDto {
  @ApiProperty({
    description: 'Board名称',
    example: 'My New Board',
  })
  @IsString()
  @IsDefined()
  @MinLength(1)
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Board描述',
    example: 'This is my new board',
  })
  @IsString()
  @IsDefined()
  @MaxLength(2048)
  description: string;

  @ApiProperty({
    description: 'Board图标',
    type: IconDto,
  })
  @ValidateNested()
  @Type(() => IconDto)
  @IsDefined()
  @IsNotEmptyObject()
  icon: IconDto;
}

export class UpdateBoardDto {
  @ApiProperty({
    description: 'Board ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsString()
  @IsDefined()
  id: string;

  @ApiPropertyOptional({
    description: 'Board名称',
    example: 'Updated Board Name',
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  name?: string;

  @ApiPropertyOptional({
    description: 'Board描述',
    example: 'Updated description',
  })
  @IsOptional()
  @IsString()
  @MaxLength(2048)
  description?: string;

  @ApiPropertyOptional({
    description: 'Board图标',
    type: IconDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => IconDto)
  icon?: IconDto;

  @ApiPropertyOptional({
    description: 'Board状态',
    example: 'in-progress',
  })
  @IsOptional()
  @IsIn(['in-progress', 'other'])
  status?: BoardStatus;
}

export class DeleteBoardDto {
  @ApiProperty({
    description: 'Board ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsString()
  @IsDefined()
  id: string;
}

export class GetBoardDto {
  @ApiProperty({
    description: 'Board ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsString()
  @IsDefined()
  id: string;
}

export class ListBoardsDto {
  @ApiPropertyOptional({
    description: 'Board状态过滤',
    example: 'in-progress',
  })
  @IsOptional()
  @IsIn(['in-progress', 'other'])
  status?: BoardStatus;

  @ApiPropertyOptional({
    description: '名称模糊搜索',
    example: 'project',
  })
  @IsOptional()
  @IsString()
  fuzzyName?: string;
}

export class BoardIdDto {
  @ApiProperty({
    description: 'Board ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsString()
  @IsDefined()
  id: string;
}

export class BoardWithItemsDto extends BoardDto {
  @ApiProperty({
    description: 'Snip数量',
    example: 10,
  })
  snipsCount: number;

  @ApiProperty({
    description: 'Thought数量',
    example: 5,
  })
  thoughtsCount: number;

  @ApiProperty({
    description: 'Board Items',
    type: [BoardItemDto],
  })
  boardItems: BoardItemDto[];
}

export class ListBoardsWithSomeBoardItemsDto {
  @ApiPropertyOptional({
    description: '名称模糊搜索',
    example: '我的看板',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  fuzzyName?: string;

  @ApiPropertyOptional({
    description: '看板状态',
    enum: BoardStatus,
    example: BoardStatus.InProgress,
  })
  @IsOptional()
  @IsIn([BoardStatus.InProgress, BoardStatus.Other])
  status?: BoardStatus;
}
