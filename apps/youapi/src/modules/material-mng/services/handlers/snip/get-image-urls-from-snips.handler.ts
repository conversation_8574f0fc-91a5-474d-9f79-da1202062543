/**
 * Get Image URLs from Snips Handler - 从Snips中获取图片URLs处理器
 *
 * 从指定的 snip ID 列表中筛选出图片类型的 snip，并提取其图片 URL
 */

import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { SnipTypeEnum } from '@/common/types/snip.types';
import { Image } from '../../../domain/snip/models/image.entity';
import { SnipRepository } from '../../../repositories/snip.repository';
import { GetImageUrlsFromSnipsQuery } from '../../queries/snip/get-image-urls-from-snips.query';

@QueryHandler(GetImageUrlsFromSnipsQuery)
export class GetImageUrlsFromSnipsHandler implements IQueryHandler<GetImageUrlsFromSnipsQuery> {
  private static readonly logger = new Logger(GetImageUrlsFromSnipsHandler.name);

  constructor(private readonly snipRepository: SnipRepository) {}

  async execute(query: GetImageUrlsFromSnipsQuery): Promise<string[]> {
    const { snipIds } = query;

    if (!snipIds || snipIds.length === 0) {
      return [];
    }

    try {
      // 并发获取所有 snips 并提取图片 URLs
      const imageUrlPromises = snipIds.map(async (snipId) => {
        try {
          const snip = await this.snipRepository.getById(snipId);

          // 只处理图片类型的 snip
          if (snip?.type === SnipTypeEnum.IMAGE) {
            const imageUrls = await (snip as Image).getChatImageUrl();
            return imageUrls[0] || null; // 返回第一个可用的图片 URL
          }

          return null;
        } catch (error) {
          GetImageUrlsFromSnipsHandler.logger.warn(
            `Failed to get image URL from snip ${snipId}`,
            error,
          );
          return null;
        }
      });

      // 等待所有 Promise 完成并过滤掉 null 值
      const imageUrls = await Promise.all(imageUrlPromises);
      return imageUrls.filter((url): url is string => !!url);
    } catch (error) {
      GetImageUrlsFromSnipsHandler.logger.error('Failed to get image URLs from snips', error);
      return [];
    }
  }
}
