/**
 * Organize Directory Structure Handler - 整理目录结构处理器
 * 重新组织看板中的目录结构，包括创建/更新组、移动项目、删除不需要的项目
 */

import { Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler, QueryBus } from '@nestjs/cqrs';
import { BoardGroup } from '../../../domain/board-group/models/board-group.entity';
import { BoardItemType } from '../../../domain/board-item/models/board-item.entity';
import { BoardWithItemsDto } from '../../../dto/board.dto';
import { BoardRepository } from '../../../repositories/board.repository';
import { BoardGroupRepository } from '../../../repositories/board-group.repository';
import { BoardItemRepository } from '../../../repositories/board-item.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { ThoughtRepository } from '../../../repositories/thought.repository';
import { OrganizeDirectoryStructureCommand } from '../../commands/board/organize-directory-structure.command';
import { GetBoardDetailQuery } from '../../queries/board/get-board-detail.query';

interface DirectoryItemInput {
  entity_type: 'board_group' | 'snip' | 'thought';
  entity_id?: string;
  name?: string;
  children?: DirectoryItemInput[];
}

@CommandHandler(OrganizeDirectoryStructureCommand)
export class OrganizeDirectoryStructureHandler
  implements ICommandHandler<OrganizeDirectoryStructureCommand>
{
  private readonly logger = new Logger(OrganizeDirectoryStructureHandler.name);

  constructor(
    private readonly boardRepository: BoardRepository,
    private readonly boardGroupRepository: BoardGroupRepository,
    private readonly boardItemRepository: BoardItemRepository,
    private readonly snipRepository: SnipRepository,
    private readonly thoughtRepository: ThoughtRepository,
    private readonly queryBus: QueryBus,
  ) {}

  async execute(command: OrganizeDirectoryStructureCommand): Promise<void> {
    const { boardId, directoryStructure } = command;

    // Get board details with current structure
    const board = await this.boardRepository.getById(boardId);
    const boardDetail: BoardWithItemsDto = await this.queryBus.execute(
      new GetBoardDetailQuery(board.creatorId, board.spaceId, boardId),
    );

    this.logger.log(
      `Organizing directory structure for board ${boardId} with ${directoryStructure.length} top-level items`,
    );

    // Track referenced IDs to identify items for deletion
    const referencedGroupIds = new Set<string>();
    const referencedSnipIds = new Set<string>();
    const referencedThoughtIds = new Set<string>();

    // Process the new directory structure
    for (let i = 0; i < directoryStructure.length; i++) {
      const item = directoryStructure[i] as DirectoryItemInput;
      const rank = this.generateRank(i, directoryStructure.length);

      await this.processDirectoryItem(
        item,
        boardId,
        board.creatorId,
        null, // parentBoardGroupId
        rank,
        referencedGroupIds,
        referencedSnipIds,
        referencedThoughtIds,
      );
    }

    // Clean up items not included in new structure
    await this.cleanupUnreferencedItems(
      boardDetail,
      referencedGroupIds,
      referencedSnipIds,
      referencedThoughtIds,
    );

    this.logger.log(`Directory organization completed for board ${boardId}`);
  }

  private async processDirectoryItem(
    item: DirectoryItemInput,
    boardId: string,
    creatorId: string,
    parentBoardGroupId: string | null,
    rank: string,
    referencedGroupIds: Set<string>,
    referencedSnipIds: Set<string>,
    referencedThoughtIds: Set<string>,
  ): Promise<void> {
    try {
      if (this.isGroup(item)) {
        await this.processGroup(
          item,
          boardId,
          creatorId,
          parentBoardGroupId,
          rank,
          referencedGroupIds,
          referencedSnipIds,
          referencedThoughtIds,
        );
      } else if (this.isSnip(item)) {
        await this.processSnip(item, boardId, parentBoardGroupId, rank);
        referencedSnipIds.add(item.entity_id);
      } else if (this.isThought(item)) {
        await this.processThought(item, boardId, parentBoardGroupId, rank);
        referencedThoughtIds.add(item.entity_id);
      }
    } catch (error) {
      this.logger.warn(`Failed to process directory item: ${JSON.stringify(item)}`, error);
    }
  }

  private async processGroup(
    group: DirectoryItemInput,
    boardId: string,
    creatorId: string,
    parentBoardGroupId: string | null,
    rank: string,
    referencedGroupIds: Set<string>,
    referencedSnipIds: Set<string>,
    referencedThoughtIds: Set<string>,
  ): Promise<void> {
    let groupId: string;

    if (group.entity_id) {
      // Update existing group
      groupId = group.entity_id;
      const boardGroup = await this.boardGroupRepository.getById(groupId);

      // Update group properties if changed
      if (boardGroup.name !== group.name) {
        boardGroup.update(group.name!, boardGroup.icon);
        await this.boardGroupRepository.save(boardGroup);
      }

      referencedGroupIds.add(groupId);
    } else {
      // Create new group
      const icon = this.hasThoughts(group.children || []) ? 'GroupFeather' : 'Group';
      const iconColor = this.hasThoughts(group.children || [])
        ? '--function-purple'
        : '--text-primary';

      const board = await this.boardRepository.getById(boardId);
      const boardGroupId = crypto.randomUUID();
      const boardGroup = await BoardGroup.create(boardGroupId, creatorId, boardId, group.name!, {
        name: icon,
        color: iconColor,
      });

      await this.boardGroupRepository.save(boardGroup);

      groupId = boardGroup.id;
      referencedGroupIds.add(groupId);
    }

    // Update the board item for this group
    await this.updateBoardItemLocation(
      groupId,
      BoardItemType.BOARD_GROUP,
      boardId,
      parentBoardGroupId,
      rank,
    );

    // Process children
    const children = group.children || [];
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const childRank = this.generateRank(i, children.length);

      await this.processDirectoryItem(
        child,
        boardId,
        creatorId,
        groupId,
        childRank,
        referencedGroupIds,
        referencedSnipIds,
        referencedThoughtIds,
      );
    }
  }

  private async processSnip(
    snip: DirectoryItemInput,
    boardId: string,
    parentBoardGroupId: string | null,
    rank: string,
  ): Promise<void> {
    await this.updateBoardItemLocation(
      snip.entity_id!,
      BoardItemType.SNIP,
      boardId,
      parentBoardGroupId,
      rank,
    );
  }

  private async processThought(
    thought: DirectoryItemInput,
    boardId: string,
    parentBoardGroupId: string | null,
    rank: string,
  ): Promise<void> {
    await this.updateBoardItemLocation(
      thought.entity_id!,
      BoardItemType.THOUGHT,
      boardId,
      parentBoardGroupId,
      rank,
    );
  }

  private async updateBoardItemLocation(
    entityId: string,
    entityType: BoardItemType,
    boardId: string,
    parentBoardGroupId: string | null,
    rank: string,
  ): Promise<void> {
    // Find existing board item or create if needed
    const boardItem = await this.boardItemRepository.findByEntityId(entityType, entityId);

    if (boardItem) {
      // Update existing board item
      // Update board item location - this would need to be implemented
      // For now, we'll focus on the basic organization
      // boardItem.updateLocation(parentBoardGroupId, rank);
      await this.boardItemRepository.save(boardItem);
    }
    // If board item doesn't exist, it should be created by the entity creation process
    // This handler focuses on organizing existing items
  }

  private async cleanupUnreferencedItems(
    boardDetail: BoardWithItemsDto,
    referencedGroupIds: Set<string>,
    referencedSnipIds: Set<string>,
    referencedThoughtIds: Set<string>,
  ): Promise<void> {
    for (const item of boardDetail.boardItems) {
      try {
        if (item.entityType === BoardItemType.BOARD_GROUP && item.boardGroupId) {
          if (!referencedGroupIds.has(item.boardGroupId)) {
            const boardGroup = await this.boardGroupRepository.getById(item.boardGroupId);
            boardGroup.delete();
            await this.boardGroupRepository.save(boardGroup);
          }
        } else if (item.entityType === BoardItemType.SNIP && item.snipId) {
          if (!referencedSnipIds.has(item.snipId)) {
            // Delete snip - would need proper implementation
            // await this.snipRepository.delete(item.snipId);
            this.logger.warn(`Snip deletion not yet implemented: ${item.snipId}`);
          }
        } else if (item.entityType === BoardItemType.THOUGHT && item.thoughtId) {
          if (!referencedThoughtIds.has(item.thoughtId)) {
            // Delete thought - would need proper implementation
            // await this.thoughtRepository.delete(item.thoughtId);
            this.logger.warn(`Thought deletion not yet implemented: ${item.thoughtId}`);
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to cleanup unreferenced item: ${item.id}`, error);
      }
    }
  }

  private generateRank(index: number, total: number): string {
    // Generate lexicographic ranks for ordering
    // Simple implementation using index/total approach
    const normalized = (index + 1) / (total + 1);
    return normalized.toString().padEnd(10, '0');
  }

  private isGroup(item: DirectoryItemInput): boolean {
    return item.entity_type === 'board_group';
  }

  private isSnip(item: DirectoryItemInput): boolean {
    return item.entity_type === 'snip';
  }

  private isThought(item: DirectoryItemInput): boolean {
    return item.entity_type === 'thought';
  }

  private hasThoughts(children: DirectoryItemInput[]): boolean {
    return children.some((child) => child.entity_type === 'thought');
  }
}
