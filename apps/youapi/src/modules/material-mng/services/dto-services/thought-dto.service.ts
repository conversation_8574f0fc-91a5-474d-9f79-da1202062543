import { Injectable } from '@nestjs/common';
import { Thought } from '../../domain/thought/models/thought.entity';
import { ThoughtDto } from '../../dto/thought.dto';

@Injectable()
export class ThoughtDtoService {
  toDto(thought: Thought): ThoughtDto {
    return Object.assign(new ThoughtDto(), {
      id: thought.id,
      createdAt: thought.createdAt,
      updatedAt: thought.updatedAt,
      spaceId: thought.spaceId,
      creatorId: thought.creatorId,
      title: thought.title,
      titleType: thought.titleType,
      content: thought.content
        ? {
            raw: thought.content.raw,
            plain: thought.content.plain,
          }
        : undefined,
      visibility: thought.visibility,
      position: thought.position,
      boardItem: {
        id: thought.position.boardItemId,
        boardId: thought.position.boardId,
        rank: thought.position.rank,
        parentBoardGroupId: thought.position.parentBoardGroupId,
        thoughtId: thought.id,
      },
      boardIds: [thought.position.boardId],
      type: 'thought', // 临时代码，用于 swagger 生成 spec 所使用，目的在于区分不同类型的实体，没有实际业务作用
    });
  }

  toDtoList(thoughts: Thought[]): ThoughtDto[] {
    return thoughts.map((thought) => this.toDto(thought));
  }
}
