import { Injectable } from '@nestjs/common';
import { BoardGroup } from '../../domain/board-group/models/board-group.entity';
import { BoardGroupDto } from '../../dto/board-group.dto';

@Injectable()
export class BoardGroupDtoService {
  /**
   * 将 BoardGroup 领域对象转换为 DTO
   */
  toDto(boardGroup: BoardGroup): BoardGroupDto {
    return Object.assign(new BoardGroupDto(), {
      id: boardGroup.id,
      createdAt: boardGroup.createdAt,
      updatedAt: boardGroup.updatedAt,
      creatorId: boardGroup.creatorId,
      boardId: boardGroup.boardId,
      name: boardGroup.name,
      icon: boardGroup.icon,
      type: boardGroup.type,
      boardItem: boardGroup.boardItem
        ? {
            id: boardGroup.boardItem.id,
            boardId: boardGroup.boardItem.boardId,
            boardGroupId: boardGroup.boardItem.boardGroupId,
            rank: boardGroup.boardItem.rank,
            createdAt: boardGroup.boardItem.createdAt,
            updatedAt: boardGroup.boardItem.updatedAt,
          }
        : undefined,
    });
  }

  /**
   * 将 BoardGroup 领域对象列表转换为 DTO 列表
   */
  toDtoList(boardGroups: BoardGroup[]): BoardGroupDto[] {
    return boardGroups.map((boardGroup) => this.toDto(boardGroup));
  }
}
