/**
 * Chat Module - 聊天模块
 * 组织聊天相关的控制器、服务和依赖
 * 采用 CQRS 模式，包含命令处理器
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/*
 */

import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { DaoModule } from '@/dao/dao.module';
import { AiModule } from '@/modules/ai/ai.module';
import { IamModule } from '@/modules/iam/iam.module';
import { ChatShortcutController } from './controllers/chat-shortcut.controller';
import { ChatV1Controller } from './controllers/chat-v1.controller';
import { ChatV2Controller } from './controllers/chat-v2.controller';
import { ChatRepository } from './repositories/chat.repository';
import { MessageRepository } from './repositories/message.repository';
import { ShortcutRepository } from './repositories/shortcut.repository';
import { ChatAggregateService } from './services/chat-aggregate.service';
import { CreateChatHandler } from './services/handlers/create-chat.handler';
import { CreateEmptyChatHandler } from './services/handlers/create-empty-chat.handler';
import { CreateShortcutHandler } from './services/handlers/create-shortcut.handler';
import { DeleteChatHandler } from './services/handlers/delete-chat.handler';
import { DeleteShortcutHandler } from './services/handlers/delete-shortcut.handler';
import { GetChatDetailHandler } from './services/handlers/get-chat-detail.handler';
import { GetChatDetailByOriginHandler } from './services/handlers/get-chat-detail-by-origin.handler';
import { ListChatHistoryHandler } from './services/handlers/list-chat-history.handler';
import { ListShortcutsHandler } from './services/handlers/list-shortcuts.handler';
import { MoveShortcutHandler } from './services/handlers/move-shortcut.handler';
import { PatchShortcutHandler } from './services/handlers/patch-shortcut.handler';
import { QueryChatDetailsByOriginHandler } from './services/handlers/query-chat-details-by-origin.handler';
import { RegenerateMessageHandler } from './services/handlers/regenerate-message.handler';
import { SaveMessagesHandler } from './services/handlers/save-messages.handler';
import { SendMessageHandler } from './services/handlers/send-message.handler';
import { UpdateChatOriginHandler } from './services/handlers/update-chat-origin.handler';
import { LegacyCompatibilityService } from './services/legacy-compatibility.service';
import { ShortcutDtoService } from './services/shortcut-dto.service';

const CommandHandlers = [
  CreateChatHandler,
  CreateEmptyChatHandler,
  CreateShortcutHandler,
  DeleteChatHandler,
  DeleteShortcutHandler,
  MoveShortcutHandler,
  PatchShortcutHandler,
  RegenerateMessageHandler,
  SaveMessagesHandler,
  SendMessageHandler,
  UpdateChatOriginHandler,
];
const QueryHandlers = [
  GetChatDetailHandler,
  GetChatDetailByOriginHandler,
  ListChatHistoryHandler,
  ListShortcutsHandler,
  QueryChatDetailsByOriginHandler,
];
const Repositories = [ChatRepository, MessageRepository, ShortcutRepository];
const Services = [ChatAggregateService, LegacyCompatibilityService, ShortcutDtoService];

@Module({
  imports: [CqrsModule, DaoModule, AiModule, IamModule],
  controllers: [ChatV1Controller, ChatV2Controller, ChatShortcutController],
  providers: [...Services, ...Repositories, ...CommandHandlers, ...QueryHandlers],
  exports: [],
})
export class ChatModule {}
