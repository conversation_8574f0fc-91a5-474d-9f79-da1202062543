/**
 * Chat Detail V1 DTO - V1 API 聊天详情数据传输对象
 *
 * V1 API 保持向后兼容性，使用传统的字段结构：
 * - Assistant消息使用 content, reasoning, events 字段
 * - 不使用 blocks 结构
 *
 * 这个DTO专门用于V1 API的响应格式化
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { LLMs, MessageEvent, MessageRoleEnum, MessageStatusEnum } from '@/common/types';
import { ChatOriginDto } from '../chat.dto';

/**
 * V1 API 用户消息格式
 */
export class UserMessageV1Dto {
  @ApiProperty({ description: 'Message ID' })
  id: string;

  @ApiProperty({ description: 'Chat ID' })
  chatId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({
    description: 'Message role',
    enum: [MessageRoleEnum.USER],
    enumName: 'MessageRoleEnum',
  })
  role: MessageRoleEnum.USER;

  @ApiProperty({
    description: 'Message status',
    enum: MessageStatusEnum,
    enumName: 'MessageStatusEnum',
  })
  status: MessageStatusEnum;

  @ApiProperty({ description: 'Message content' })
  content: string;

  @ApiProperty({ description: 'Message origin context' })
  origin: ChatOriginDto;

  @ApiPropertyOptional({ description: 'Selected text context' })
  selection?: string;
}

/**
 * V1 API 助手消息格式
 * 使用传统的 content, reasoning, events 字段而不是 blocks
 */
export class AssistantMessageV1Dto {
  @ApiProperty({ description: 'Message ID' })
  id: string;

  @ApiProperty({ description: 'Chat ID' })
  chatId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({
    description: 'Message role',
    enum: [MessageRoleEnum.ASSISTANT],
    enumName: 'MessageRoleEnum',
  })
  role: MessageRoleEnum.ASSISTANT;

  @ApiProperty({
    description: 'Message status',
    enum: MessageStatusEnum,
    enumName: 'MessageStatusEnum',
  })
  status: MessageStatusEnum;

  @ApiProperty({
    description: 'AI model used',
    enum: LLMs,
    enumName: 'LLMs',
  })
  @IsEnum(LLMs)
  model: LLMs;

  @ApiProperty({ description: 'Trace ID for debugging' })
  traceId: string;

  @ApiProperty({ description: 'Message content (extracted from content blocks)' })
  content: string;

  @ApiPropertyOptional({ description: 'Reasoning text (extracted from reasoning blocks)' })
  reasoning?: string;

  @ApiProperty({ description: 'Message events (converted from tool blocks)', type: [Object] })
  events: MessageEvent[];

  @ApiPropertyOptional({ description: 'Error information if any' })
  error?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Reasoning start timestamp' })
  reasoningBeginAt?: string;

  @ApiPropertyOptional({ description: 'Reasoning end timestamp' })
  reasoningEndAt?: string;
}

export type MessageV1Dto = UserMessageV1Dto | AssistantMessageV1Dto;

/**
 * V1 API 聊天详情格式
 */
export class ChatDetailV1Dto {
  @ApiProperty({ description: 'Chat ID' })
  id: string;

  @ApiProperty({ description: 'Creator user ID' })
  creatorId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'Chat title' })
  title: string;

  @ApiProperty({ description: 'Chat origin context' })
  origin: ChatOriginDto;

  @ApiPropertyOptional({ description: 'Board ID if applicable' })
  boardId?: string;

  @ApiProperty({
    description: 'Chat messages',
    type: [Object], // Using Object since we have union types
  })
  messages: MessageV1Dto[];

  @ApiPropertyOptional({ description: 'Associated board IDs' })
  boardIds?: string[];

  @ApiPropertyOptional({ description: 'Board item information' })
  boardItem?: any; // TODO: Define proper BoardItem type
}

/**
 * V1 API 聊天详情列表响应格式
 */
export class ChatDetailListV1Dto {
  @ApiProperty({
    description: 'List of chat details',
    type: [ChatDetailV1Dto],
  })
  data: ChatDetailV1Dto[];
}

export class CreateChatV1Dto {
  @ApiProperty({ description: 'Chat origin context', type: ChatOriginDto })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  origin: ChatOriginDto;

  @ApiProperty({ description: 'Initial message for the chat' })
  @IsString()
  @MinLength(1)
  message: string;

  @ApiProperty({ description: 'AI model to use' })
  @IsString()
  chatModel: string;

  @ApiPropertyOptional({ description: 'Selected text context' })
  @IsString()
  @IsOptional()
  selection?: string;

  @ApiPropertyOptional({ description: 'Enable web search', default: false })
  @IsBoolean()
  @IsOptional()
  webSearch?: boolean;
}

export class SendMessageV1Dto {
  @ApiProperty({ description: 'Chat ID to send message to' })
  @IsString()
  @IsUUID()
  chatId: string;

  @ApiProperty({ description: 'Message content' })
  @IsString()
  @MinLength(1)
  message: string;

  @ApiPropertyOptional({ description: 'Selected text context' })
  @IsString()
  @IsOptional()
  selection?: string;

  @ApiPropertyOptional({
    description: 'AI model to use',
    enum: LLMs,
    enumName: 'LLMs',
  })
  @IsEnum(LLMs)
  @IsOptional()
  chatModel?: LLMs;

  @ApiPropertyOptional({ description: 'Enable web search', default: false })
  @IsBoolean()
  @IsOptional()
  webSearch?: boolean;
}

export class RegenerateMessageV1Dto {
  @ApiProperty({ description: 'Chat ID containing the message to regenerate' })
  @IsString()
  @IsUUID()
  chatId: string;

  @ApiProperty({ description: 'User message ID to regenerate from' })
  @IsString()
  @IsUUID()
  userMessageId: string;

  @ApiPropertyOptional({
    description: 'AI model to use for regeneration',
    enum: LLMs,
    enumName: 'LLMs',
  })
  @IsEnum(LLMs)
  @IsOptional()
  chatModel?: LLMs;

  @ApiPropertyOptional({ description: 'Enable web search for regeneration' })
  @IsOptional()
  webSearch?: boolean;
}

export class QueryChatDetailsByOriginDto extends ChatOriginDto {}

/**
 * Update Chat Title DTO - Update chat title
 */
export class UpdateChatTitleDto {
  @ApiProperty({ description: 'Chat ID to update title for' })
  @IsString()
  @IsUUID()
  chatId: string;

  @ApiProperty({ description: 'New chat title', maxLength: 200 })
  @IsString()
  @MinLength(1)
  title: string;
}

/**
 * Save Messages DTO - Save messages to a chat
 */
export class SaveMessagesDto {
  @ApiPropertyOptional({ description: 'Chat origin context', type: ChatOriginDto })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  @IsOptional()
  origin?: ChatOriginDto;

  @ApiPropertyOptional({ description: 'Chat ID to save messages to' })
  @IsString()
  @IsUUID()
  @IsOptional()
  chatId?: string;

  @ApiProperty({ description: 'User message content' })
  @IsString()
  @MinLength(1)
  userMessage: string;

  @ApiPropertyOptional({ description: 'User message selection context' })
  @IsString()
  @IsOptional()
  userMessageSelection?: string;

  @ApiProperty({ description: 'Assistant message content' })
  @IsString()
  @MinLength(1)
  assistantMessage: string;
}

/**
 * Get Suggestion by Webpage DTO - Get chat suggestions for a webpage
 */
export class GetSuggestionByWebpageDto {
  @ApiProperty({ description: 'Webpage URL' })
  @IsString()
  @IsUrl()
  url: string;

  @ApiProperty({ description: 'Webpage content' })
  @IsString()
  content: string;
}

/**
 * List History for Chat Assistant DTO - List chat history for assistant
 */
export class ListHistoryForChatAssistantDto {
  @ApiPropertyOptional({ description: 'Query parameters' })
  @IsOptional()
  query?: {
    limit?: number;
    offset?: number;
    order?: 'asc' | 'desc';
  };

  @ApiProperty({ description: 'User ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'Board ID' })
  @IsString()
  @IsUUID()
  boardId: string;

  @ApiPropertyOptional({ description: 'Chat origin context', type: ChatOriginDto })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  @IsOptional()
  origin?: ChatOriginDto;
}

/**
 * Chat Suggestions Result DTO - Response for chat suggestions
 */
export class ChatSuggestionsResultDto {
  @ApiProperty({ description: 'Array of suggestion strings', type: [String] })
  @IsArray()
  @IsString({ each: true })
  suggestions: string[];
}

/**
 * Chat Detail Response DTO - Response for chat detail operations
 */
export class ChatDetailResponseDto {
  @ApiProperty({ description: 'Chat ID' })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Chat title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Chat creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Chat last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Chat messages', type: [Object] })
  @IsArray()
  messages: any[];

  @ApiPropertyOptional({ description: 'Board ID if chat belongs to a board' })
  @IsString()
  @IsUUID()
  @IsOptional()
  boardId?: string;

  @ApiPropertyOptional({ description: 'Chat origin type' })
  @IsString()
  @IsOptional()
  originType?: string;

  @ApiPropertyOptional({ description: 'Chat origin ID' })
  @IsString()
  @IsOptional()
  originId?: string;
}

/**
 * Chat List Response DTO - Response for chat list operations
 */
export class ChatListResponseDto {
  @ApiProperty({ description: 'Array of chat details', type: [ChatDetailResponseDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatDetailResponseDto)
  data: ChatDetailResponseDto[];

  @ApiProperty({ description: 'Total number of chats' })
  @IsNumber()
  total: number;
}
