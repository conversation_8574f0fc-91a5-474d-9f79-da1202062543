/**
 * Chat Controller - 聊天控制器
 * 处理聊天相关的HTTP请求
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/deleteChat/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/createChat/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/getChatDetail/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/getChatDetailForAssistant/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/listChatModels/route.ts
 */

import { Body, Controller, HttpCode, Post, Sse, UseInterceptors } from '@nestjs/common';
import { ApiBody, ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PublicRoute } from '@/common/decorators/public.decorator';
import { SSEErrorHandlerInterceptor } from '@/common/interceptors/sse-error-handler.interceptor';
import { ChatOriginTypeEnum, MessageModeEnum } from '@/common/types';
import {
  ASK_AI_MODELS,
  ChatModeEnum,
  LLMs,
  MODEL_DEFINITION,
  type ModelDefinition,
  ToolNames,
} from '@/common/types/chat.types';
import { observableToSse } from '@/modules/ai/utils/toObservable';
import { v2ObservableToStreamMessage } from '@/modules/chat/utils/toObservable';
import { BaseController } from '@/shared/base.controller';
import { ChatIdDto } from '../dto/chat.dto';
import {
  StreamChunkedDataDto,
  StreamDataDto,
  StreamErrorDto,
  StreamMessageEventDto,
  StreamStatusUpdateDto,
  StreamTextDto,
  StreamTextStartDto,
  StreamTextStopDto,
  StreamTraceDto,
} from '../dto/stream-message.dto';
import {
  ChatDetailListV1Dto,
  ChatDetailV1Dto,
  CreateChatV1Dto,
  QueryChatDetailsByOriginDto,
  RegenerateMessageV1Dto,
  SaveMessagesDto,
  SendMessageV1Dto,
} from '../dto/v1/chat-v1.dto';
import { CreateChatCommand } from '../services/commands/create-chat.command';
import { DeleteChatCommand } from '../services/commands/delete-chat.command';
import { RegenerateMessageCommand } from '../services/commands/regenerate-message.command';
import { SaveMessagesCommand } from '../services/commands/save-messages.command';
import { SendMessageCommand } from '../services/commands/send-message.command';
import { GetChatDetailQuery } from '../services/queries/get-chat-detail.query';
import { QueryChatDetailsByOriginQuery } from '../services/queries/query-chat-details-by-origin.query';

const V1_SSE_API_RESPONSE = {
  'text/event-stream': {
    schema: {
      type: 'object',
      oneOf: [
        { $ref: '#/components/schemas/StreamMessageEventDto' },
        { $ref: '#/components/schemas/StreamDataDto' },
        { $ref: '#/components/schemas/StreamChunkedDataDto' },
        { $ref: '#/components/schemas/StreamErrorDto' },
        { $ref: '#/components/schemas/StreamTextDto' },
        { $ref: '#/components/schemas/StreamTraceDto' },
        { $ref: '#/components/schemas/StreamTextStartDto' },
        { $ref: '#/components/schemas/StreamTextStopDto' },
        { $ref: '#/components/schemas/StreamStatusUpdateDto' },
      ],
    },
  },
};

@UseInterceptors(SSEErrorHandlerInterceptor)
@ApiTags('Chat V1')
@ApiExtraModels(
  // StreamMessage DTOs for SSE responses
  StreamMessageEventDto,
  StreamDataDto,
  StreamChunkedDataDto,
  StreamErrorDto,
  StreamTextDto,
  StreamTraceDto,
  StreamTextStartDto,
  StreamTextStopDto,
  StreamStatusUpdateDto,
)
@Controller('api/v1/chat')
export class ChatV1Controller extends BaseController {
  @Post('deleteChat')
  @HttpCode(200)
  @ApiOperation({ summary: 'Delete a chat' })
  @ApiBody({ type: ChatIdDto })
  @ApiResponse({
    status: 200,
    description: 'Chat deleted successfully',
    schema: { type: 'object', properties: { success: { type: 'boolean' } } },
  })
  async deleteChat(@Body() dto: ChatIdDto) {
    const command = new DeleteChatCommand(dto.chatId);
    await this.commandBus.execute(command);
    return { success: true };
  }

  @Post('createChat')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Create a new chat',
    description:
      'Creates a new chat with the first message and returns a server-sent event stream with V1-compatible stream messages',
  })
  @ApiBody({ type: CreateChatV1Dto })
  @ApiResponse({
    status: 200,
    description: 'Server-sent events stream with V1-compatible stream messages',
    content: V1_SSE_API_RESPONSE,
  })
  @Sse()
  async createChat(@Body() dto: CreateChatV1Dto) {
    const command = new CreateChatCommand({
      userId: this.getUserId(),
      boardId: dto.origin.type === ChatOriginTypeEnum.BOARD ? dto.origin.id : undefined,
      origin: dto.origin,
      version: 'v1',
      message: dto.message,
      mode: ChatModeEnum.CHAT,
      chatModel: dto.chatModel as LLMs,
      selection: dto.selection,
      tools: dto.webSearch ? { [ToolNames.GOOGLE_SEARCH]: { useTool: 'auto' } } : undefined,
      command: undefined,
      atReferences: undefined,
      shortcut: undefined,
    });
    const observable = await this.commandBus.execute(command);
    return observableToSse(v2ObservableToStreamMessage(observable));
  }

  @Post('getChatDetail')
  @HttpCode(200)
  @ApiOperation({ summary: 'Get chat details' })
  @ApiBody({ type: ChatIdDto })
  @ApiResponse({
    status: 200,
    description: 'Chat details retrieved successfully',
    type: ChatDetailV1Dto,
  })
  async getChatDetail(@Body() dto: ChatIdDto) {
    const query = new GetChatDetailQuery(dto.chatId, 'v1');
    return await this.queryBus.execute(query);
  }

  @Post('queryChatDetailsByOrigin')
  @HttpCode(200)
  @ApiOperation({ summary: 'Query chat details by origin' })
  @ApiBody({ type: QueryChatDetailsByOriginDto })
  @ApiResponse({
    status: 200,
    description: 'Chat details queried successfully',
    type: ChatDetailListV1Dto,
  })
  async queryChatDetailsByOrigin(
    @Body() dto: QueryChatDetailsByOriginDto,
  ): Promise<ChatDetailListV1Dto> {
    const query = new QueryChatDetailsByOriginQuery(dto, this.getUserId(), 'v1');
    const data = await this.queryBus.execute(query);
    return { data };
  }

  @Post('listChatModels')
  @PublicRoute()
  @HttpCode(200)
  @ApiOperation({ summary: 'List available chat models' })
  @ApiResponse({
    status: 200,
    description: 'Available chat models retrieved successfully',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          description: { type: 'string' },
        },
      },
    },
  })
  async listChatModels(): Promise<Record<string, ModelDefinition>> {
    // 返回所有可用的聊天模型配置
    return ASK_AI_MODELS.reduce<Record<string, ModelDefinition>>((accu, curr) => {
      accu[curr] = MODEL_DEFINITION.get(curr)!;
      return accu;
    }, {});
  }

  @Post('regenerateMessage')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Regenerate a chat message',
    description:
      'Regenerates an AI assistant message and returns a server-sent event stream with V1-compatible stream messages',
  })
  @ApiBody({ type: RegenerateMessageV1Dto })
  @ApiResponse({
    status: 200,
    description: 'Server-sent events stream with V1-compatible regenerated message stream',
    content: V1_SSE_API_RESPONSE,
  })
  @Sse()
  async regenerateMessage(@Body() dto: RegenerateMessageV1Dto) {
    // 创建并执行重新生成命令
    const command = new RegenerateMessageCommand(
      this.getUserId(),
      dto.chatId,
      dto.userMessageId,
      'v1',
      dto.webSearch ? { [ToolNames.GOOGLE_SEARCH]: { useTool: 'auto' } } : undefined,
    );
    const observable = await this.commandBus.execute(command);
    return observableToSse(v2ObservableToStreamMessage(observable));
  }

  @Post('saveMessages')
  @HttpCode(200)
  @ApiOperation({ summary: 'Save user and assistant messages to a chat' })
  @ApiBody({ type: SaveMessagesDto })
  @ApiResponse({ status: 200, type: ChatDetailV1Dto })
  async saveMessages(@Body() dto: SaveMessagesDto) {
    const command = new SaveMessagesCommand(
      this.getUserId(),
      dto.chatId,
      dto.userMessage,
      dto.assistantMessage,
      'v1',
      dto.origin,
      dto.userMessageSelection,
    );
    return await this.commandBus.execute(command);
  }

  @Post('sendMessage')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Send a message to an existing chat',
    description:
      'Sends a message to an existing chat and returns a server-sent event stream with V1-compatible stream messages',
  })
  @ApiBody({ type: SendMessageV1Dto })
  @ApiResponse({
    status: 200,
    description: 'Server-sent events stream with V1-compatible AI response stream',
    content: V1_SSE_API_RESPONSE,
  })
  @Sse()
  async sendMessage(@Body() dto: SendMessageV1Dto) {
    const command = new SendMessageCommand({
      userId: this.getUserId(),
      chatId: dto.chatId,
      message: dto.message,
      selection: dto.selection,
      atReferences: [],
      ...(dto.webSearch ? { tools: { [ToolNames.GOOGLE_SEARCH]: { useTool: 'auto' } } } : {}),
      messageMode: MessageModeEnum.ASK,
      command: undefined,
      shortcut: undefined,
    });
    const observable = await this.commandBus.execute(command);
    return observableToSse(v2ObservableToStreamMessage(observable));
  }
}
