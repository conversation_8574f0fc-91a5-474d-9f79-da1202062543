import { CoreMessage, LanguageModelV1, streamText, TextStreamPart } from 'ai';
import { catchError, EMPTY, merge, Observable, ReplaySubject, Subject, tap } from 'rxjs';
import { RestError } from '@/common/errors';
import { YouapiClsService } from '@/common/services/cls.service';
import {
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  GenerationStatusEnum,
  MessageStatusEnum,
  MODEL_DEFINITION,
  type ToolCallResult,
  ToolNames,
} from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { UserDomainService } from '@/domain/user';
import { Generation } from '@/modules/ai/domain/models/generation.entity';
import { PromptService } from '@/modules/ai/prompt/index.service';
import { BaseRunner } from '@/modules/ai/runners/base';
import { ToolCallService } from '@/modules/ai/tools/tool-call.service';
import { streamToObservable } from '@/modules/ai/utils/toObservable';
import { UserRepository } from '@/modules/iam/repositories/user.repository';
import { Chat } from '../../domain/chat/models/chat.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { MessageRepository } from '../../repositories/message.repository';
import { SendMessageCommand } from '../commands/send-message.command';

/**
 * Chat Assistant 基类 Runner
 * 仅承载通用逻辑，不同模式下的业务逻辑拼装见 AskRunner/AgentRunner
 */
export abstract class ChatRunner extends BaseRunner {
  protected readonly traceName: string;
  protected readonly promptService: PromptService;
  protected readonly userRepository: UserRepository;
  protected readonly userDomainService: UserDomainService;
  protected readonly chatRepository: ChatRepository;
  protected readonly messageRepository: MessageRepository;
  protected readonly youapiClsService: YouapiClsService;
  protected toolCallService: ToolCallService;
  protected generations: Generation[] = [];

  constructor(
    protected readonly chat: Chat,
    protected readonly userId: string,
  ) {
    super();
    this.promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    this.userRepository = ApplicationContext.getProvider<UserRepository>(UserRepository);
    this.userDomainService = ApplicationContext.getProvider<UserDomainService>(UserDomainService);
    this.chatRepository = ApplicationContext.getProvider<ChatRepository>(ChatRepository);
    this.messageRepository = ApplicationContext.getProvider<MessageRepository>(MessageRepository);
    this.youapiClsService = ApplicationContext.getProvider<YouapiClsService>(YouapiClsService);
    this.toolCallService = ApplicationContext.getProvider<ToolCallService>(ToolCallService);
  }

  protected getGeneratedCoreMessages(): CoreMessage[] {
    return this.generations.flatMap((generation) => generation.generatedMessages);
  }

  protected assembleApiParameters(model: LanguageModelV1): Parameters<typeof streamText>[0] {
    const params = super.assembleApiParameters(model);
    const messages = this.promptMessages;

    // 加上 anthropic cacheControl
    params.messages = [
      ...messages.slice(0, -1),
      {
        ...messages[messages.length - 1],
        providerOptions: {
          anthropic: {
            cacheControl: { type: 'ephemeral' },
          },
        },
      },
    ];

    params.tools = this.toolCallService.getVercelToolCalls(this.currentGeneration.tools);
    params.toolChoice =
      typeof this.currentGeneration.toolChoice === 'string'
        ? this.currentGeneration.toolChoice
        : {
            type: 'tool',
            toolName: this.currentGeneration.toolChoice.function.name,
          };

    return params;
  }

  /**
   * 调用 streamText 并将 AI SDK 文本流转为 CompletionStreamChunk 流
   * 这里仅存放跟 Generation/CompletionBlock 的调用逻辑
   * Generation/CompletionBlock 内部实现应放置 AI 模块，避免与 Chat 业务逻辑耦合
   */
  protected async streamText<T>(
    subject: Subject<CompletionStreamChunk<T>>,
    command: SendMessageCommand,
  ) {
    const model = this.getModel(false);
    const traceNode = await this.startTrace(model, this.traceName, {
      inheritTrace: true,
    });

    let onFirstChunk = false;
    const params = this.assembleApiParameters(model);

    // fake reasoning 控制流
    const controlChannel = new ReplaySubject<TextStreamPart<any>>();
    const modelDefinition = MODEL_DEFINITION.get(this.model);
    if (modelDefinition.extra?.fake_reasoning) {
      controlChannel.next({
        type: 'reasoning',
        textDelta: '',
      });
    }

    const generator = streamText({
      ...params,
      toolCallStreaming: true,
      onChunk: () => {
        if (onFirstChunk) return;

        onFirstChunk = true;
        traceNode.update({
          completionStartTime: new Date(),
        });
      },
      onFinish: async (result) => {
        const { usage, finishReason } = result;

        if (finishReason === 'tool-calls') {
          traceNode.span({
            name: 'tool-calls',
            input: result.toolCalls,
          });

          for (const toolCall of result.toolCalls) {
            // 从 Generation 中获取对应的 ToolCompletionBlock
            const completionBlock = this.currentGeneration.getToolBlockByToolId(
              toolCall.toolCallId,
            );
            if (!completionBlock) {
              throw new Error(`ToolCompletionBlock not found for toolCall: ${toolCall.toolCallId}`);
            }

            // 调用工具
            const toolResult: ToolCallResult = await this.toolCallService.callTool(
              toolCall.toolName as ToolNames,
              {
                chat: this.chat,
                userId: this.userId,
                parsedParams: toolCall.args,
                completionBlock: completionBlock,
              },
              traceNode,
              subject,
            );

            // 将 CoreToolMessage 转换为 TextStreamPart
            const toolResultPart: TextStreamPart<any> = {
              type: 'tool-result' as const,
              toolCallId: toolCall.toolCallId,
              toolName: toolCall.toolName,
              args: toolCall.args,
              result: toolResult, // 包括 response 和 中间结果
            };

            // 通过 controlChannel 发送，这样 Generation.processTextStream 就能接收到
            controlChannel.next(toolResultPart);
          }

          traceNode.end({
            metadata: {
              success: true,
              finishReason,
            },
          });
          controlChannel.complete();
          this.currentGeneration.setStatus(GenerationStatusEnum.SUCCESS);
          await this.setupGeneration(command);
          await this.streamText(subject, command);
          return;
        }

        subject.complete();
        this.currentGeneration.setStatus(GenerationStatusEnum.SUCCESS);

        const usageReport = this.buildUsageReport(usage);
        traceNode.end({
          ...usageReport,
          metadata: {
            success: true,
            finishReason,
          },
        });
      },
      onError: ({ error }) => {
        subject.error(error);
        this.currentGeneration.setStatus(GenerationStatusEnum.FAILED);

        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    const streamTextObservable = streamToObservable(generator);
    this.currentGeneration.processTextStream(
      merge(controlChannel, streamTextObservable),
      subject,
      traceNode,
    );
  }

  /**
   * 通过 setupGeneration 实现每轮调用 streamText 时的参数差异
   * 例如：修改模型参数、添加历史消息、修改当轮可用 tool 等
   * 在具体业务 Runner 中实现，例如 AskRunner/AgentRunner
   */
  protected abstract setupGeneration(command: SendMessageCommand): Promise<void>;

  /**
   * Chat Assistant 生成消息流入口
   * 处理跟 Message 相关的业务逻辑与消息发送（例如 Message 的新建、更新状态、更新字段等）
   */
  async generate<T>(command: SendMessageCommand): Promise<Observable<CompletionStreamChunk<any>>> {
    if (!this.chat.getLastAssistantMessage()) {
      throw new Error('Chat has no assistant message');
    }
    if (!this.chat.getLastUserMessage()) {
      throw new Error('Chat has no user message');
    }

    const messageSubject = new ReplaySubject<CompletionStreamChunk<any>>();
    messageSubject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Chat',
      data: this.chat.toCompletionStreamChunk(),
    });

    if (this.chat.getLastUserMessage().isNew) {
      await this.messageRepository.save(this.chat.getLastUserMessage());
      this.chat.getLastUserMessage().commit();
    }
    messageSubject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Message',
      data: this.chat.getLastUserMessage().toCompletionStreamChunk(),
    });

    if (this.chat.getLastAssistantMessage().isNew) {
      await this.messageRepository.save(this.chat.getLastAssistantMessage());
      this.chat.getLastAssistantMessage().commit();
    }
    const assistantMessage = this.chat.getLastAssistantMessage();
    messageSubject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Message',
      data: assistantMessage.toCompletionStreamChunk(),
    });
    messageSubject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'Message',
      targetId: assistantMessage.id,
      path: 'trace_id',
      data: assistantMessage.traceId,
    });

    await this.setupGeneration(command);

    // 开始生成消息流
    const streamSubject = new ReplaySubject<CompletionStreamChunk<any>>();
    await this.streamText<T>(streamSubject, command);

    const updateStatus = async (status: MessageStatusEnum) => {
      messageSubject.next({
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: assistantMessage.id,
        path: 'status',
        data: status,
      });
      assistantMessage.updateStatus(status);
      await this.messageRepository.save(assistantMessage);
      assistantMessage.commit();

      if (
        [MessageStatusEnum.DONE, MessageStatusEnum.ERROR, MessageStatusEnum.ABORT].includes(status)
      ) {
        messageSubject.complete();
      }
    };

    // 订阅 streamSubject 来处理完成和错误
    streamSubject
      .asObservable()
      .pipe(
        tap({
          complete: async () => {
            await updateStatus(MessageStatusEnum.DONE);
          },
        }),
        catchError(async (error) => {
          // 在停止之前发送错误作为 CompletionStreamChunk
          const errorChunk: CompletionStreamChunk<any> = {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'Message',
            targetId: assistantMessage.id,
            path: 'error',
            data:
              error instanceof RestError
                ? error.json('')
                : {
                    code: (error as Error).name,
                    status: 400,
                    message: (error as Error).message,
                  },
          };

          messageSubject.next(errorChunk);
          assistantMessage.setError(error);
          await updateStatus(MessageStatusEnum.ERROR);
          return EMPTY;
        }),
      )
      .subscribe(); // 订阅以确保 tap 和 catchError 被执行

    return merge(messageSubject.asObservable(), streamSubject.asObservable());
  }
}
