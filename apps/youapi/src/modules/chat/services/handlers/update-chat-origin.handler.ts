/**
 * Update Chat Origin Command Handler - 更新聊天来源命令处理器
 * 处理更新聊天来源的业务逻辑
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ChatRepository } from '../../repositories/chat.repository';
import { UpdateChatOriginCommand } from '../commands/update-chat-origin.command';

@Injectable()
@CommandHandler(UpdateChatOriginCommand)
export class UpdateChatOriginHandler implements ICommandHandler<UpdateChatOriginCommand> {
  private readonly logger = new Logger(UpdateChatOriginHandler.name);

  constructor(private readonly chatRepository: ChatRepository) {}

  async execute(command: UpdateChatOriginCommand): Promise<void> {
    try {
      const { chatId, origin } = command;

      // 获取聊天实体
      const chat = await this.chatRepository.findById(chatId);
      if (!chat) {
        throw new Error(`Chat with id ${chatId} not found`);
      }

      // 更新聊天来源
      chat.updateOrigin(origin);

      // 保存更新
      await this.chatRepository.save(chat);

      this.logger.log(`Updated chat origin for chat ${chatId} to ${origin.type}`);
    } catch (error) {
      this.logger.error(`Error updating chat origin for chat ${command.chatId}:`, error);
      throw error;
    }
  }
}
