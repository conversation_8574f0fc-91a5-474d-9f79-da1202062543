/**
 * Audio Generate Tool Service - 音频生成工具服务
 * 提供文本转语音功能，支持多种语音模型和参数配置
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/audio_generate.ts
 * - apps/youapi/src/domain/chat/tool_call/audio-generate.service.ts
 */

import { Injectable } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import type { CompletionStreamChunk } from '@/common/types';
import {
  CompletionStreamModeEnum,
  CompletionStreamReplaceChunk,
  MessageAudioGenerateResultSchema,
  StreamChunkUnion,
  ToolNames,
} from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { CreateUploadFileDto } from '../../material-mng/dto/snip/file.dto';
import { CreateVoiceCommand } from '../../material-mng/services/commands/snip/create-voice.command';
import { PatchVoiceTranscriptCommand } from '../../material-mng/services/commands/snip/patch-voice-transcript.command';
import { AudioRunner } from '../runners/audio';
import { BaseToolService } from './base.service';

// 定义音频生成参数的类型
const AudioGenerateParameters = z.object({
  text: z
    .string()
    .describe(
      'Text to be synthesized. Character limit < 5000 chars. Paragraph markers will be replaced by line breaks. (To manually add a pause, insert the phrase <#x#> where x refers to the number of seconds to pause. Supports 0.01-99.99, with an accuracy of at most 2 decimal places)',
    ),
  title: z.string().describe('The title of the audio.'),
  voice: z
    .string()
    .describe(
      'Desired voice. Both system voice ids and cloned voice ids are supported. System voice ids are listed below: Wise_Woman, Friendly_Person, Inspirational_girl, Deep_Voice_Man, Calm_Woman, Casual_Guy, Lively_Girl, Patient_Man, Young_Knight, Determined_Man, Lovely_Girl, Decent_Boy, Imposing_Manner, Elegant_Man, Abbess, Sweet_Girl_2, Exuberant_Girl. Default to Wise_Woman.',
    ),
  speed: z
    .number()
    .min(0.5)
    .max(2.0)
    .default(1)
    .describe(
      'The speed of the audio from 0.5 to 2.0. Default to 1. The speed of the generated speech. Larger values indicate faster speech.',
    ),
  vol: z
    .number()
    .min(0.1)
    .max(10.0)
    .default(1)
    .describe(
      'The volume of the audio from 0.1 to 10.0. Default to 1. Larger values indicate larger volumes.',
    ),
  pitch: z
    .number()
    .min(-12)
    .max(12)
    .default(0)
    .describe(
      'The pitch of the audio from -12 to 12. Default to 0. The pitch of the generated speech. A value of 0 corresponds to default voice output.',
    ),
  emotion: z
    .enum(['happy', 'sad', 'angry', 'fearful', 'disgusted', 'surprised', 'neutral'])
    .default('happy')
    .describe(
      'The emotion of the audio from happy, sad, angry, fearful, disgusted, surprised, neutral. Default to happy.',
    ),
  parent_board_group_id: z.string().optional().describe('Optional. Board group ID'),
});

type AudioGenerateParameters = z.infer<typeof AudioGenerateParameters>;

const AudioGenerateToolResultSchema = MessageAudioGenerateResultSchema;

const AudioGenerateDescription = `use this tool to generate an audio from text with a given voice. This tool may incur costs. Use only when explicitly requested by the user. Create an audio snip if explicitly requested by the user. The tool returns an album_url (cover image) that can be used with create_snip_by_url tool's album_urls parameter.`;

@Injectable()
export class AudioGenerateService extends BaseToolService {
  readonly toolName: ToolNames = ToolNames.AUDIO_GENERATE;
  readonly toolDescription = AudioGenerateDescription;
  readonly toolParameters = AudioGenerateParameters;
  readonly toolOutputSchema = AudioGenerateToolResultSchema;
  readonly toolExecutionTimeout = 120000; // 2 minutes for audio generation
  readonly maxCalls = 3;

  async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { parsedParams, userId, chat, completionBlock } = parameters;
    const { text } = parsedParams;
    const {
      title,
      voice,
      speed,
      vol,
      pitch,
      emotion,
      parent_board_group_id: common_parent_board_group_id,
    } = parsedParams;
    const common_board_id = chat.boardId;

    if (!text) {
      return {
        response: 'Invalid text',
      };
    }

    const createSnip = chat.isInsideNewBoardWorkflow();
    const spaceId = await this.getSpaceId(userId);

    // Generate audio using AudioRunner
    const audioRunner = new AudioRunner();
    const { hash, audio_url, subtitleFile, extraInfo, image_url } = await audioRunner.generate({
      userId,
      text,
      title,
      voice,
      speed: speed?.toString() || '1',
      vol: vol?.toString() || '1',
      pitch: pitch?.toString() || '0',
      emotion,
    });

    const result: z.infer<typeof AudioGenerateToolResultSchema> = {
      title,
      audio_url,
      subtitle_file: subtitleFile,
      extra_info: extraInfo,
      album_url: image_url,
    };

    if (createSnip) {
      const childSpan = span.span({
        name: 'create-voice-snip',
        input: {
          audio_url,
          user_id: userId,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        },
      });

      try {
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: {
            status: 'processing',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);

        const snipResult = await this.createVoice({
          file: {
            name: title,
            hash,
            isPublic: true,
          },
          title,
          boardId: common_board_id,
          parentBoardGroupId: common_parent_board_group_id,
          heroImageUrl: image_url,
          playUrl: audio_url,
          chatId: chat.id,
          spaceId,
          userId,
        });

        if (snipResult) {
          // Download subtitle file and create transcript block
          if (subtitleFile) {
            await this.patchVoiceTranscript({
              userId,
              spaceId,
              snipId: snipResult.id,
              subtitleFileUrl: subtitleFile,
            });
          }

          childSpan.event({
            name: 'create-voice-snip-success',
            input: { url: audio_url, user_id: userId, snip_id: snipResult?.id },
            output: snipResult,
          });

          const successResult = {
            status: 'success',
            board_id:
              common_board_id ||
              (snipResult?.board_ids && snipResult.board_ids.length > 0
                ? snipResult.board_ids[0]
                : null), // 如果没有提供 common_board_id, 则使用 snip 返回的第一个 board_id
            vo: snipResult,
          };

          result.snip = successResult;
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: successResult,
            path: 'tool_result.snip',
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          childSpan.end();
        }
      } catch (error) {
        childSpan.event({
          name: 'create-voice-snip-failed',
          input: {
            audio_url,
            user_id: userId,
            error: error instanceof Error ? (error as any)?.name : String(error),
          },
        });
        childSpan.end({
          level: 'ERROR' as any,
          statusMessage: (error as Error).message,
        });
        result.snip = {
          status: 'failed',
        };

        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: {
            status: 'failed',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);
      }
    }

    return {
      response: `audio finished generating. The audio url is ${audio_url}. Don't display this audio url in markdown syntax, e.g [Alt text](audio_url). ${result.album_url ? `An album cover has been generated at ${result.album_url}. This album_url can be used as album_urls parameter when creating snips.` : ''}`,
      result,
    };
  }

  private async createVoice(params: {
    spaceId: string;
    userId: string;
    title: string;
    boardId: string;
    playUrl?: string;
    file: CreateUploadFileDto;
    parentBoardGroupId?: string;
    heroImageUrl?: string;
    chatId: string;
  }): Promise<any> {
    const command = new CreateVoiceCommand(
      params.spaceId, // spaceId
      params.userId, // creatorId
      params.title, // title
      undefined, // webpage
      undefined, // showNotes
      undefined, // authors
      params.heroImageUrl, // heroImageUrl
      undefined, // publishedAt
      params.playUrl, // playUrl
      undefined, // extra
      params.file, // VoiceFileDto
      params.boardId, // string
      params.parentBoardGroupId, // string
      params.chatId, // chatId
    );
    const voiceDto = await this.commandBus.execute(command);
    return {
      id: voiceDto.id,
      board_ids: voiceDto.boardIds || [params.boardId],
    };
  }

  private async patchVoiceTranscript(params: {
    userId: string;
    spaceId: string;
    snipId: string;
    subtitleFileUrl: string;
  }): Promise<void> {
    const command = new PatchVoiceTranscriptCommand(
      params.userId,
      params.spaceId,
      params.snipId,
      params.subtitleFileUrl,
    );

    await this.commandBus.execute(command);
  }
}
