/**
 * Tool Call Service - 工具调用服务管理器
 * 管理所有工具服务的注册和调用
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { ToolSet } from 'ai';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import { LLMError, RestError, ToolCallTimeoutError } from '@/common/errors';
import { type CompletionStreamChunk, type StreamChunkUnion, ToolNames } from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { AudioGenerateService } from './audio-generate.tool';
import { BaseToolService } from './base.service';
import { CreateBoardService } from './create-board.tool';
import { CreateSnipByUrlService } from './create-snip-by-url.tool';
// import { GoogleSearchService } from './google-search.tool';
import { DiagramGenerateService } from './diagram-generate.tool';
// import { BoardSearchService } from './board-search.tool';
import { EditThoughtService } from './edit-thought.tool';
import { ImageGenerateService } from './image-generate.tool';
// import { LibrarySearchService } from './library-search.tool';
import { OrganizeDirectoryStructureService } from './organize-directory-structure.tool';

@Injectable()
export class ToolCallService {
  private readonly logger = new Logger(ToolCallService.name);
  private readonly toolServices = new Map<ToolNames, BaseToolService>();

  constructor(
    // All tool services are now properly converted to NestJS providers
    // private readonly librarySearchService: LibrarySearchService,
    // private readonly boardSearchService: BoardSearchService,
    // private readonly googleSearchService: GoogleSearchService,
    private readonly audioGenerateService: AudioGenerateService,
    private readonly imageGenerateService: ImageGenerateService,
    private readonly diagramGenerateService: DiagramGenerateService,
    private readonly createBoardService: CreateBoardService,
    private readonly createSnipByUrlService: CreateSnipByUrlService,
    private readonly organizeDirectoryStructureService: OrganizeDirectoryStructureService,
    private readonly editThoughtService: EditThoughtService,
  ) {
    this.registerToolServices();
  }

  /**
   * Register all tool services
   * 注册所有工具服务
   */
  private registerToolServices(): void {
    // 内容生成类工具
    this.toolServices.set(ToolNames.AUDIO_GENERATE, this.audioGenerateService);
    this.toolServices.set(ToolNames.IMAGE_GENERATE, this.imageGenerateService);
    this.toolServices.set(ToolNames.DIAGRAM_GENERATE, this.diagramGenerateService);

    // 搜索类工具
    // this.toolServices.set(ToolNames.LIBRARY_SEARCH, this.librarySearchService);
    // this.toolServices.set(ToolNames.GOOGLE_SEARCH, this.googleSearchService);
    // this.toolServices.set(ToolNames.BOARD_SEARCH, this.boardSearchService);

    // 材料相关工具
    this.toolServices.set(ToolNames.CREATE_SNIP_BY_URL, this.createSnipByUrlService);
    this.toolServices.set(ToolNames.EDIT_THOUGHT, this.editThoughtService);

    // Board 管理工具
    this.toolServices.set(ToolNames.CREATE_BOARD, this.createBoardService);
    this.toolServices.set(
      ToolNames.ORGANIZE_DIRECTORY_STRUCTURE,
      this.organizeDirectoryStructureService,
    );
  }

  /**
   * 执行工具调用并提供异常处理
   */
  async callTool(
    toolName: ToolNames,
    toolContext: ToolFunctionParameters,
    span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const service = this.toolServices.get(toolName);
    if (!service) {
      throw new Error(`Tool service ${toolName} not found`);
    }

    const TOOL_EXECUTION_TIMEOUT = this.getToolExecutionTimeout(toolName) * 1000 || 60000;
    try {
      const result = await Promise.race([
        service.execute(toolContext, span, subject),
        new Promise<never>((_, reject) =>
          setTimeout(
            () => reject(new ToolCallTimeoutError({ tool_name: toolName })),
            TOOL_EXECUTION_TIMEOUT,
          ),
        ),
      ]);

      return result;
    } catch (error) {
      this.logger.error(`Tool call failed: ${toolName}/${toolContext.completionBlock.id}`);
      this.logger.error(error);

      const code = (error as LLMError)?.code || 'unknown_error';
      return {
        result: (error instanceof RestError
          ? error.json('')
          : {
              code,
              status: 400,
              message: error.message,
              trace_id: span.traceId,
            }) as Record<string, unknown>,
        response: `ERROR: ${error.message || 'failed to call tool' + toolName}`,
      };
    }
  }

  /**
   * 提供 Vercel 的工具参数调用
   */
  getVercelToolCalls(toolNames: ToolNames[]): ToolSet {
    const availableTools = this.getAvailableToolNames();
    this.logger.debug(`Requested tools: ${toolNames.join(', ')}`);
    this.logger.debug(`Available tools: ${availableTools.join(', ')}`);

    return toolNames.reduce((acc, toolName) => {
      const service = this.toolServices.get(toolName);
      // 临时去掉检测逻辑
      // if (!service) {
      //   throw new Error(`${toolName} not found`);
      // }
      // acc[toolName] = service.getVercelTool();
      if (service) {
        acc[toolName] = service.getVercelTool();
      }
      return acc;
    }, {} as ToolSet);
  }

  /**
   * Check if a tool is available
   * 检查工具是否可用
   */
  isToolAvailable(toolName: ToolNames): boolean {
    return this.toolServices.has(toolName);
  }

  /**
   * Get available tool names
   * 获取可用的工具类型
   */
  getAvailableToolNames(): ToolNames[] {
    return Array.from(this.toolServices.keys());
  }

  /**
   * 获取工具调用超时时间
   */
  getToolExecutionTimeout(toolName: ToolNames): number {
    const service = this.toolServices.get(toolName);
    return service.toolExecutionTimeout;
  }
}
