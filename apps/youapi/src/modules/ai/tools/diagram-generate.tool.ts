/**
 * Diagram Generate Tool Service - 图表生成工具服务
 * 提供SVG图表生成功能，支持多种图表类型和尺寸配置
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/diagram_generate.ts
 * - apps/youapi/src/domain/chat/tool_call/diagram-generate.service.ts
 */

import { Injectable } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import { lastValueFrom, Subject } from 'rxjs';
import z from 'zod';
import type { CompletionStreamChunk } from '@/common/types';
import {
  CompletionStreamAppendStringChunk,
  CompletionStreamModeEnum,
  CompletionStreamReplaceChunk,
  MessageDiagramGenerateResultSchema,
  StreamChunkUnion,
  ToolNames,
} from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { CreateUploadFileDto } from '../../material-mng/dto/snip/file.dto';
import { CreateImageCommand } from '../../material-mng/services/commands/snip/create-image.command';
import { ImageRunner } from '../runners/image';
import { BaseToolService } from './base.service';

// 定义图表生成参数的类型
const DiagramGenerateParameters = z.object({
  text: z.string().describe('The text to generate a diagram for'),
  title: z.string().describe('The title of the diagram.'),
  type: z
    .enum([
      'flowchart',
      'mindmap',
      'sequence diagram',
      'quadrant chart',
      'timeline',
      'PPT or keynote slide',
      'other',
    ])
    .describe('The type of the diagram.'),
  size: z
    .enum(['auto', 'square', 'portrait', 'landscape'])
    .default('auto')
    .describe('The size of the generated diagram. Default to auto.'),
  parent_board_group_id: z.string().optional().describe('Optional. Board group ID'),
});

type DiagramGenerateParameters = z.infer<typeof DiagramGenerateParameters>;

const DiagramGenerateToolResultSchema = MessageDiagramGenerateResultSchema;

const DiagramGenerateDescription = `use this tool to generate a diagram or chart in SVG format.
This tool is good at generating more structured diagrams like flowchart, mindmap, sequence diagrams, quadrant chart, timeline, PPT or keynote slides(Generate one page in one call) etc.
But if user ask for mermaid syntax, don't use this tool. Create an image snip if explicitly requested by the user.`;

@Injectable()
export class DiagramGenerateService extends BaseToolService {
  readonly toolName: ToolNames = ToolNames.DIAGRAM_GENERATE;
  readonly toolDescription = DiagramGenerateDescription;
  readonly toolParameters = DiagramGenerateParameters;
  readonly toolOutputSchema = DiagramGenerateToolResultSchema;
  readonly toolExecutionTimeout = 240000; // 4 minutes for diagram generation
  readonly maxCalls = 3;

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { parsedParams, userId, chat, completionBlock } = parameters;
    const {
      text,
      title,
      type,
      size = 'auto',
      parent_board_group_id: common_parent_board_group_id,
    } = parsedParams;
    const common_board_id = chat.getBoardId();

    if (!text) {
      return {
        response: 'Invalid text',
      };
    }

    const createSnip = chat.isInsideNewBoardWorkflow();
    const spaceId = await this.getSpaceId(userId);

    // Generate initial SVG using ImageRunner
    const imageRunner = new ImageRunner();
    const diagramResult = await imageRunner.generateDiagram({
      text,
      type,
      size,
    });

    const generatedSVG = diagramResult.text || '';

    // Stream initial SVG content
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: generatedSVG,
      path: 'tool_result.svg',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    let accumulatedContent = '';
    try {
      // Optimize SVG with streaming using ImageRunner.optimizeSVG
      const optimizedStream = await imageRunner.optimizeSVG({
        svg: generatedSVG,
        type,
      });

      let received = false;
      optimizedStream.subscribe({
        next: (chunk) => {
          if (!received) {
            received = true;
            subject.next({
              mode: CompletionStreamModeEnum.REPLACE,
              targetType: 'CompletionBlock',
              targetId: completionBlock.id,
              data: '',
              path: 'tool_result.svg',
            } as CompletionStreamReplaceChunk<StreamChunkUnion>);
          }
          // Extract text content from different chunk types
          let chunkContent = '';
          if (chunk.type === 'text-delta') {
            chunkContent = chunk.textDelta;
          }
          if (chunkContent) {
            accumulatedContent += chunkContent;
          }
          subject.next({
            mode: CompletionStreamModeEnum.APPEND_STRING,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: chunkContent,
            path: 'tool_result.svg',
          } as CompletionStreamAppendStringChunk);
        },
        error: (_error) => {
          // 失败了就使用未经优化的 SVG
          accumulatedContent = imageRunner.formatSVG(generatedSVG);
        },
        complete: () => {
          // Stream completed
        },
      });

      // Wait for the stream to complete using lastValueFrom
      await lastValueFrom(optimizedStream, { defaultValue: null });
    } catch (error) {
      // 失败了就使用未经优化的 SVG
      accumulatedContent = imageRunner.formatSVG(generatedSVG);
    }

    // Upload SVG using ImageRunner API
    const finalSvg = imageRunner.formatSVG(accumulatedContent || generatedSVG);
    const { hash, svg_url: image_url } = await imageRunner.uploadSVG(finalSvg);
    const svg = finalSvg;

    const result: z.infer<typeof DiagramGenerateToolResultSchema> = {
      svg,
      image_url,
    };

    if (createSnip) {
      const childSpan = span.span({
        name: 'create-image-snip',
        input: {
          image_url,
          user_id: userId,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        },
      });

      try {
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: {
            status: 'processing',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);

        const snipResult = await this.createImage({
          spaceId,
          userId,
          chatId: chat.id,
          boardId: common_board_id,
          parentBoardGroupId: common_parent_board_group_id,
          file: {
            name: title,
            hash,
            isPublic: true,
          },
          title,
        });

        if (snipResult) {
          childSpan.event({
            name: 'create-image-snip-success',
            input: { url: image_url, user_id: userId, snip_id: snipResult?.id },
            output: snipResult,
          });

          const successResult = {
            status: 'success',
            board_id:
              common_board_id ||
              (snipResult?.board_ids && snipResult.board_ids.length > 0
                ? snipResult.board_ids[0]
                : null), // 如果没有提供 common_board_id, 则使用 snip 返回的第一个 board_id
            vo: snipResult,
          };

          result.snip = successResult;
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: successResult,
            path: 'tool_result.snip',
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          childSpan.end();
        }
      } catch (error) {
        childSpan.event({
          name: 'create-image-snip-failed',
          input: {
            image_url,
            user_id: userId,
            error: error instanceof Error ? (error as any)?.name : String(error),
          },
        });
        childSpan.end({
          level: 'ERROR' as any,
          statusMessage: (error as Error).message,
        });
        result.snip = {
          status: 'failed',
        };

        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: {
            status: 'failed',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);
      }
    }

    return {
      response: `Diagram finished generating, it can be accessed at ${image_url}. Use this URL only as input for other tool calls. This URL will be presented using customized UI component, don't repeat this URL in your response. Doing so will be penalized.`,
      result,
    };
  }

  private async createImage(params: {
    spaceId: string;
    userId: string;
    chatId: string;
    boardId: string;
    file: CreateUploadFileDto;
    title: string;
    parentBoardGroupId?: string;
  }): Promise<any> {
    const command = new CreateImageCommand(
      params.spaceId, // spaceId
      params.userId, // userId
      params.title,
      undefined, // extra
      params.file, // CreateUploadFileDto
      undefined, // webpage
      params.boardId,
      params.parentBoardGroupId,
      params.chatId,
    );

    const imageDto = await this.commandBus.execute(command);
    return {
      id: imageDto.id,
      board_ids: imageDto.boardIds || [params.boardId],
    };
  }
}
