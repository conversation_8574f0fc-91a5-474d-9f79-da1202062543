// /**
//  * Library Search Tool Service - 库搜索工具服务
//  * 在YouMind中搜索用户处理的所有材料
//  *
//  * Migrated from:
//  * - youapp/src/domain/chat/tool_call/library_search.ts
//  * - apps/youapi/src/domain/chat/tool_call/library-search.service.ts
//  */

// import { Injectable, Logger } from '@nestjs/common';
// import { LangfuseSpanClient } from 'langfuse-core';
// import z from 'zod';
// import { ChatModeEnum, ToolNames } from '@/common/types';
// import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
// import { BoardDomainService } from '@/domain/board';
// import type {
//   AtReferenceForContextBuilding,
//   MessageAnalyzedContextType,
// } from '@/domain/chat/context/types';
// import { ContextManager } from '../contexts/context.manager';
// import { BaseToolService } from './base.service';

// // 定义库搜索参数的类型
// const LibrarySearchParameters = z.object({
//   query: z
//     .string()
//     .describe(
//       'A high quality search query to be used to semantically search in YouMind, convert relative date to concrete date if applicable',
//     ),
// });

// type LibrarySearchParameters = z.infer<typeof LibrarySearchParameters>;

// const LibrarySearchToolResultSchema = z.object({
//   query: z.string(),
//   results: z.array(z.any()),
//   contextString: z.string().optional(),
// });

// const LibrarySearchDescription = `Search all materials processed by current user in YouMind.
// Use this tool only if the user ask to search all the content in YouMind, with clear instructions like "search all boards", "search my library", "search everything", etc,
// or stated that use "library search" tool.
// If users' query includes "@youmind", you should prioritize using this tool.`;

// @Injectable()
// export class LibrarySearchService extends BaseToolService {
//   private readonly logger = new Logger(LibrarySearchService.name);

//   readonly toolName: ToolNames = ToolNames.LIBRARY_SEARCH;
//   readonly toolDescription = LibrarySearchDescription;
//   readonly toolParameters = LibrarySearchParameters;
//   readonly toolOutputSchema = LibrarySearchToolResultSchema;
//   readonly toolExecutionTimeout = 60000; // 1 minute for library search
//   readonly maxCalls = 3;

//   constructor(
//     private readonly boardDomain: BoardDomainService,
//     private readonly contextManager: ContextManager,
//   ) {
//     super();
//   }

//   async execute(
//     parameters: ToolFunctionParameters,
//     span: LangfuseSpanClient,
//   ): Promise<ToolCallResult> {
//     const { user_id, chat, parsed_params, completion_block } = parameters;
//     const { query } = parsed_params;

//     const finalAtReferences: AtReferenceForContextBuilding[] = [
//       {
//         entity_type: 'library',
//       },
//     ];

//     const isNewBoardChat = chat.mode === ChatModeEnum.NEW_BOARD;
//     const availableTokens = 40000;

//     const analysis = {
//       needsContext: true,
//       contextType: 'relevant' as MessageAnalyzedContextType,
//       searchKeywords: query,
//     };

//     // Send status message
//     // Note: Status messages are now handled by the caller
//     // subject.next({
//     //   mode: CompletionStreamModeEnum.REPLACE,
//     //   targetType: 'CompletionBlock',
//     //   targetId: completion_block.id,
//     //   data: `Searching in YouMind`,
//     //   path: 'tool_result.statusMessage',
//     // } as CompletionStreamChunk<StreamChunkUnion>);

//     // const contextResult = await this.contextManager.buildReferenceContexts({
//     //   userId: user_id,
//     //   at_references: finalAtReferences,
//     //   availableTokens,
//     //   searchKeywords: query,
//     //   strategy: 'semantic',
//     // });

//     const contextResult = {
//       atReferencesRetrieved: [],
//     };

//     const searchResults = contextResult.atReferencesRetrieved.map((ref) => ({
//       id: ref.atReferenceId,
//       type: ref.atReferenceType,
//       chunks: ref.chunks.map((chunk) => chunk.chunk),
//     }));

//     const contextString = contextResult.atReferencesRetrieved
//       .map((ref) => ref.chunks.map((chunk) => chunk.chunk).join('\n'))
//       .join('\n\n');

//     return {
//       response: `Found ${searchResults.length} relevant items in your YouMind library.`,
//       result: {
//         query,
//         results: searchResults,
//         contextString,
//       },
//     };
//   }
// }
