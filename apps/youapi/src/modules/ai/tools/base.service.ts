/**
 * Base Tool Service - 工具服务基类
 * 定义所有工具服务的通用接口和模式
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/type.ts
 */

import { Injectable } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { Tool } from 'ai';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import { ZodSchema } from 'zod';
import type { CompletionStreamChunk, StreamChunkUnion, ToolNames } from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { GetSpaceByUserIdQuery } from '@/modules/iam/services/queries/space/get-space-by-user-id.query';

/**
 * Base abstract class for all tool services
 * 所有工具服务的抽象基类
 */
@Injectable()
export abstract class BaseToolService {
  abstract readonly toolName: ToolNames;
  abstract readonly toolDescription: string;
  abstract readonly toolParameters: ZodSchema;
  abstract readonly toolOutputSchema?: ZodSchema;
  abstract readonly toolExecutionTimeout: number;

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
  ) {}

  protected async getSpaceId(userId: string): Promise<string> {
    const { id } = await this.queryBus.execute(new GetSpaceByUserIdQuery(userId));
    return id;
  }

  /**
   * Execute the tool function
   * 执行工具函数
   */
  abstract execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult>;

  getVercelTool(): Tool {
    return {
      type: 'function',
      parameters: this.toolParameters,
      description: this.getToolDescription(),
    };
  }

  getToolName(): string {
    return this.toolName;
  }

  getToolDescription(): string {
    return this.toolDescription;
  }
}
