// /**
//  * Board Search Tool Service - 板块搜索工具服务
//  * 处理板块搜索相关的工具调用
//  *
//  * Migrated from:
//  * - youapp/src/domain/chat/tool_call/board_search.ts
//  * - apps/youapi/src/domain/chat/tool_call/board-search.service.ts
//  */

// import { Injectable, Logger } from '@nestjs/common';
// import { LangfuseSpanClient } from 'langfuse-core';
// import z from 'zod';
// import { MessageRoleEnum, ToolNames } from '@/common/types';
// import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
// import { ContextManager } from '../contexts/context.manager';
// import { BaseToolService } from './base.service';

// // 定义板块搜索参数的类型
// const BoardSearchParameters = z.object({
//   contextType: z
//     .enum(['full', 'relevant'])
//     .describe(
//       "Choose 'full' for a broad overview of a board item or the entire board; choose 'relevant' when only a focused excerpt is needed.",
//     ),
//   query: z
//     .string()
//     .describe(
//       "Semantic search string. Required for 'relevant'; leave empty ('') when contextType is 'full'.",
//     ),
// });

// type BoardSearchParameters = z.infer<typeof BoardSearchParameters>;

// const BoardSearchToolResultSchema = z.object({
//   query: z.string(),
//   results: z.array(z.any()),
//   contextString: z.string().optional(),
// });

// const BoardSearchDescription = `Use this tool only if the user's request can be answered with content already present in the current board in by board directory structure,
// or users' query explicitly mentions the board (e.g. "@[@BOARD_NAME](id:BOARD_ID;type:board)") or board group (e.g. "@[@BOARD_GROUP_NAME](id:BOARD_GROUP_ID;type:board_group)")
// and you need extra information from those boards or board groups to fulfill the user's request.

// 1. Check the board directory first.
// - If none of its \`title\` or \`entity_type\` values are topically related to the user's prompt, do not call the tool.
// - If at least one item is relevant, build the \`parameters\` object as described below.

// 2. \`contextType\` rules
// - "relevant" – The user asks for a specific fact, explanation, or narrow slice of information.
// - "full" – The user wants a broad survey or summary of an entire board item.

// 3. \`query\` construction
// - For "relevant": distill the user prompt into 3-8 core keywords/phrases; resolve pronouns/back-references; replace relative dates ("yesterday") with concrete dates, match the date time format of the board item if applicable.
// - For "full": leave \`query\` empty ("")`;

// @Injectable()
// export class BoardSearchService extends BaseToolService {
//   private readonly logger = new Logger(BoardSearchService.name);

//   readonly toolName: ToolNames = ToolNames.BOARD_SEARCH;
//   readonly toolDescription = BoardSearchDescription;
//   readonly toolParameters = BoardSearchParameters;
//   readonly toolOutputSchema = BoardSearchToolResultSchema;
//   readonly toolExecutionTimeout = 60000; // 1 minute for board search
//   readonly maxCalls = 3;

//   constructor(private readonly contextManager: ContextManager) {
//     super();
//   }

//   async execute(
//     parameters: ToolFunctionParameters,
//     span: LangfuseSpanClient,
//   ): Promise<ToolCallResult> {
//     const {
//       user_id,
//       chat,
//       parsed_params,
//       completion_block,
//       boardId,
//       isInsideNewBoardWorkflow: createSnip,
//     } = parameters;
//     const { query, contextType } = parsed_params;

//     const lastUserMessage = chat.messages.findLast((m) => m.role === MessageRoleEnum.USER);
//     const atReferences = lastUserMessage?.at_references;
//     if (!atReferences) {
//       return { response: 'No context needed' };
//     }

//     const availableTokens = 40000;

//     // Send status message
//     // Note: Status messages are now handled by the caller
//     // subject.next({
//     //   mode: CompletionStreamModeEnum.REPLACE,
//     //   targetType: 'CompletionBlock',
//     //   targetId: completion_block.id,
//     //   data: `Searching in board`,
//     //   path: 'tool_result.statusMessage',
//     // } as CompletionStreamChunk<StreamChunkUnion>);

//     // const contextResult = await this.contextManager.buildReferenceContexts({
//     //   userId: user_id,
//     //   at_references: atReferences,
//     //   availableTokens,
//     //   searchKeywords: query,
//     //   strategy: contextType === 'full' ? 'full' : 'semantic',
//     // });

//     const contextResult = {
//       atReferencesRetrieved: [],
//     };

//     const searchResults = contextResult.atReferencesRetrieved.map((ref) => ({
//       id: ref.atReferenceId,
//       type: ref.atReferenceType,
//       chunks: ref.chunks.map((chunk) => chunk.chunk),
//     }));

//     const contextString = contextResult.atReferencesRetrieved
//       .map((ref) => ref.chunks.map((chunk) => chunk.chunk).join('\n'))
//       .join('\n\n');

//     return {
//       response: `Found ${searchResults.length} relevant items in the board.`,
//       result: {
//         query,
//         results: searchResults,
//         contextString,
//       },
//     };
//   }
// }
