/**
 * Update Completion Block DTO - 更新完成块数据传输对象
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/universe/apps/youapi/src/modules/chat/dto/v1/chat-v1.dto.ts
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsObject, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateCompletionBlockDto {
  @ApiProperty({ description: 'Block ID to update' })
  @IsString()
  @IsUUID()
  blockId: string;

  @ApiPropertyOptional({ description: 'Tool ID' })
  @IsString()
  @IsOptional()
  toolId?: string;

  @ApiPropertyOptional({ description: 'Tool name' })
  @IsString()
  @IsOptional()
  toolName?: string;

  @ApiPropertyOptional({ description: 'Tool response' })
  @IsString()
  @IsOptional()
  toolResponse?: string;

  @ApiPropertyOptional({ description: 'Tool arguments' })
  @IsObject()
  @IsOptional()
  toolArguments?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tool result' })
  @IsObject()
  @IsOptional()
  toolResult?: Record<string, any>;
}
