import { Logger } from '@nestjs/common';
import { AggregateRoot } from '@nestjs/cqrs';
import { CoreMessage, GenerateTextResult, TextStreamPart } from 'ai';
import { ChatPromptClient, LangfuseGenerationClient, LangfuseSpanClient } from 'langfuse';
import { ChatCompletionToolChoiceOption } from 'openai/resources/index.js';
import { interval, Observable, Subject } from 'rxjs';
import { concatMap, finalize, takeUntil } from 'rxjs/operators';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import {
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  DEFAULT_AI_CHAT_MODEL,
  GenerationStatusEnum,
  LLMs,
  ToolNames,
} from '@/common/types';
import type { ToolCallResult } from '@/common/types/tool-call.types';
import { ApplicationContext } from '@/common/utils/application-context';
import {
  CompletionBlock,
  ContentCompletionBlock,
  ReasoningCompletionBlock,
  ToolCompletionBlock,
} from '@/modules/ai/domain/models/completion-block.entity';
import { CompletionBlockRepository } from '@/modules/ai/repositories/completion-block.repository';
import { blocksToMessages } from '../../utils/blocksToMessages';

export interface ImageEditMask {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ImageEditOptions {
  prompt: string;
  size?: '1024x1024' | '1536x1024' | '1024x1536' | 'auto';
  quality?: 'standard' | 'hd';
  mask?: ImageEditMask[];
}

export interface ImageGenerationOptions {
  prompt: string;
  size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
  quality?: 'standard' | 'hd';
  style?: 'vivid' | 'natural';
  n?: number;
}

export class Generation extends AggregateRoot {
  protected readonly logger = new Logger(Generation.name);

  public model: LLMs;
  public tools: ToolNames[];
  public toolChoice: ChatCompletionToolChoiceOption;
  public modelOptions: Record<string, any>;
  public traceMetadata: Record<string, string>;
  public bizArgs: Record<string, any>;
  public prediction: string[];

  protected status: GenerationStatusEnum = GenerationStatusEnum.PENDING;
  protected trace: LangfuseGenerationClient | null = null;

  protected prompt: ChatPromptClient;
  public promptName: string;
  public promptVersion: number;
  public promptMessages: CoreMessage[];
  public generatedMessages: CoreMessage[];
  public blocks: CompletionBlock[];

  constructor(param: {
    model?: LLMs;
    tools?: ToolNames[];
    toolChoice?: ChatCompletionToolChoiceOption;
    bizArgs?: Record<string, any>;
    modelOptions?: Record<string, any>;
    traceMetadata?: Record<string, string>;
    prompt?: ChatPromptClient;
    promptMessages?: CoreMessage[];
    prediction?: string[];
  }) {
    super();

    this.prompt = param.prompt;
    const promptConfig = param.prompt?.config as {
      model?: LLMs;
      temperature?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };

    this.model = param.model || promptConfig?.model || DEFAULT_AI_CHAT_MODEL;
    this.tools = param.tools || [];
    this.toolChoice = param.toolChoice || (this.tools.length > 0 ? 'auto' : 'none');

    const temperature = param.modelOptions?.temperature || promptConfig?.temperature || 0.5;
    const topP = param.modelOptions?.topP || promptConfig?.topP || 1;
    const frequencyPenalty =
      param.modelOptions?.frequencyPenalty || promptConfig?.frequencyPenalty || 0;
    const presencePenalty =
      param.modelOptions?.presencePenalty || promptConfig?.presencePenalty || 0;

    this.bizArgs = param.bizArgs || {};
    this.prediction = param.prediction || [];
    this.traceMetadata = param.traceMetadata || {};
    this.modelOptions = {
      ...param.modelOptions,
      ...(temperature ? { temperature } : {}),
      ...(topP ? { topP } : {}),
      ...(frequencyPenalty ? { frequencyPenalty } : {}),
      ...(presencePenalty ? { presencePenalty } : {}),
    };
    this.promptName = param.prompt?.name || '';
    this.promptVersion = param.prompt?.version || 1;
    this.promptMessages = param.promptMessages || [];
    this.blocks = [];
    this.generatedMessages = [];
  }

  public setStatus(status: GenerationStatusEnum) {
    this.status = status;
  }

  public addGeneratedMessage(message: CoreMessage) {
    this.generatedMessages.push(message);
  }

  private chatMessageId: string | null = null;
  public getChatMessageId(): string | null {
    return this.chatMessageId;
  }
  public setChatMessageId(messageId: string) {
    this.chatMessageId = messageId;
    return this;
  }

  public setBizArgs(bizArgs: Record<string, any>) {
    this.bizArgs = {
      ...this.bizArgs,
      ...bizArgs,
    };
    return this;
  }

  public getToolBlockByToolId(toolId: string): ToolCompletionBlock | null {
    return (
      (this.blocks?.find(
        (block) =>
          block.type === CompletionBlockTypeEnum.TOOL &&
          (block as ToolCompletionBlock).toolId === toolId,
      ) as ToolCompletionBlock) || null
    );
  }

  /**
   * 保存所有完成块到数据库 (repository 会自动筛选新的或已修改的块)
   */
  private async saveBlocks(): Promise<void> {
    if (this.blocks.length === 0) {
      this.logger.debug('No completion blocks to save');
      return;
    }

    // TODO: 临时处理下 messageId 为空时，不保存（跟 youapp 实现保持一致）
    const blocksToSave = this.blocks.filter(
      (block) => (block.isNew || block.isModified) && block.messageId,
    );
    this.logger.debug(`Found ${blocksToSave.length} blocks to save`);

    const completionBlockRepository =
      ApplicationContext.getProvider<CompletionBlockRepository>(CompletionBlockRepository);
    await completionBlockRepository.saveMany(blocksToSave);
    this.logger.debug('Successfully saved completion blocks');
  }

  /**
   * 录入 Completion Block
   * 包含每2秒的定期持久化和完成时的最终持久化
   * @param observable
   */
  public processTextStream(
    observable: Observable<TextStreamPart<any>>,
    emitSubject?: Subject<CompletionStreamChunk<any>>,
    traceClient?: LangfuseGenerationClient,
  ) {
    this.status = GenerationStatusEnum.GENERATING;
    const subject = emitSubject || new Subject<CompletionStreamChunk<any>>();
    const traceService = ApplicationContext.getProvider<LangfuseTraceService>(LangfuseTraceService);
    const traceNode = traceClient || traceService.getCurrentGeneration();
    const spanMap = new Map<string, LangfuseSpanClient>();

    // 设置定期保存完成块的定时器 (每2秒)
    const persistenceTimer = interval(2000)
      .pipe(
        takeUntil(subject), // 当subject完成或错误时停止定时器
      )
      .subscribe(() => {
        // 异步保存，不等待结果，错误处理在 saveBlocks 内部
        this.saveBlocks().catch(this.logger.error);
      });
    const onSuccess = async () => {
      // 清理定时器
      persistenceTimer.unsubscribe();

      // 更新完成块状态
      this.blocks.forEach((block) => {
        // Tool block 的状态由 tool-result 触发，不需要在这里更新
        if (block.type !== CompletionBlockTypeEnum.TOOL && !block.isFinal()) {
          this.logger.debug(`Update block ${block.id} status to DONE`);
          block.updateStatus(CompletionBlockStatusEnum.DONE);
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetId: block.id,
            targetType: 'CompletionBlock',
            path: 'status',
            data: CompletionBlockStatusEnum.DONE,
          });
        }
      });
      await this.saveBlocks();

      // 转换为 CoreMessage
      this.generatedMessages = blocksToMessages(this.blocks);
      // 更新 Generation 状态
      this.status = GenerationStatusEnum.SUCCESS;
    };
    const onFailure = async () => {
      // 清理定时器
      persistenceTimer.unsubscribe();

      // 更新完成块状态
      this.blocks.forEach((block) => {
        if (!block.isFinal()) {
          this.logger.debug(`Update block ${block.id} status to ERROR`);
          block.updateStatus(CompletionBlockStatusEnum.ERROR);

          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetId: block.id,
            targetType: 'CompletionBlock',
            path: 'status',
            data: CompletionBlockStatusEnum.ERROR,
          });
        }
      });
      await this.saveBlocks();

      // 转换为 CoreMessage
      this.generatedMessages = blocksToMessages(this.blocks);
      // 更新 Generation 状态
      this.status = GenerationStatusEnum.FAILED;
    };
    const getOrCreateBlock = (
      type: CompletionBlockTypeEnum,
      toolCallId?: string,
      toolName?: string,
    ) => {
      let current: CompletionBlock | null = null;
      if (toolCallId) {
        current = this.getToolBlockByToolId(toolCallId);
      } else if (this.blocks.length) {
        current = this.blocks[this.blocks.length - 1];
      }

      const doCreate = !current || current.type !== type;
      const updatePrevious = doCreate && current;

      if (updatePrevious) {
        current.updateStatus(CompletionBlockStatusEnum.DONE);
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetId: current.id,
          targetType: 'CompletionBlock',
          path: 'status',
          data: CompletionBlockStatusEnum.DONE,
        });
      }
      if (doCreate) {
        if (type === CompletionBlockTypeEnum.CONTENT) {
          current = ContentCompletionBlock.createNew({
            messageId: this.chatMessageId,
            data: '',
          });
        } else if (type === CompletionBlockTypeEnum.REASONING) {
          current = ReasoningCompletionBlock.createNew({
            messageId: this.chatMessageId,
            data: '',
          });
        } else if (type === CompletionBlockTypeEnum.TOOL) {
          current = ToolCompletionBlock.createNew({
            messageId: this.chatMessageId,
            toolId: toolCallId,
            toolName,
          });

          spanMap.set(
            toolCallId,
            traceNode.span({
              name: 'tool-call-' + toolName,
              input: {},
            }),
          );
        }

        this.logger.debug(`Created block ${current.id} with messageId: ${current.messageId}`);

        this.blocks.push(current);
        subject.next({
          mode: CompletionStreamModeEnum.INSERT,
          data: current.toCompletionStreamChunk(),
          dataType: 'CompletionBlock',
        });
      }

      return current;
    };

    observable
      .pipe(
        concatMap(async (chunk) => {
          if (chunk.type === 'error') {
            await onFailure();
            return chunk;
          }

          if (chunk.type === 'finish') {
            await onSuccess();
            return chunk;
          }

          if (chunk.type === 'text-delta') {
            const current = getOrCreateBlock(
              CompletionBlockTypeEnum.CONTENT,
            ) as ContentCompletionBlock;
            (current as ContentCompletionBlock).appendContent(chunk.textDelta);

            subject.next({
              mode: CompletionStreamModeEnum.APPEND_STRING,
              data: chunk.textDelta,
              path: 'data',
              targetId: current.id,
              targetType: 'CompletionBlock',
            });

            return chunk;
          }

          if (
            chunk.type === 'reasoning' ||
            chunk.type === 'redacted-reasoning' ||
            chunk.type === 'reasoning-signature'
          ) {
            const current = getOrCreateBlock(
              CompletionBlockTypeEnum.REASONING,
            ) as ReasoningCompletionBlock;

            if (chunk.type === 'reasoning') {
              (current as ReasoningCompletionBlock).appendContent(chunk.textDelta);

              subject.next({
                mode: CompletionStreamModeEnum.APPEND_STRING,
                data: chunk.textDelta,
                path: 'data',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            } else if (chunk.type === 'redacted-reasoning') {
              (current as ReasoningCompletionBlock).appendRedactedReasoning(chunk.data);
            } else if (chunk.type === 'reasoning-signature') {
              (current as ReasoningCompletionBlock).setSignature(chunk.signature);
            }

            return chunk;
          }

          if (
            chunk.type === 'tool-call' ||
            chunk.type === 'tool-call-delta' ||
            chunk.type === 'tool-call-streaming-start' ||
            chunk.type === 'tool-result'
          ) {
            const current = getOrCreateBlock(
              CompletionBlockTypeEnum.TOOL,
              chunk.toolCallId,
              chunk.toolName,
            ) as ToolCompletionBlock;

            if (chunk.type === 'tool-call-delta') {
              current.appendPartialToolArguments(chunk.argsTextDelta);

              subject.next({
                mode: CompletionStreamModeEnum.APPEND_JSON,
                data: chunk.argsTextDelta,
                path: 'tool_arguments',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            } else if (chunk.type === 'tool-call') {
              current.startExecution(chunk.args as Record<string, unknown>);

              subject.next({
                mode: CompletionStreamModeEnum.REPLACE,
                data: current.status,
                path: 'status',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
              spanMap.get(chunk.toolCallId)?.update({
                input: chunk.args,
              });
            } else if (chunk.type === 'tool-result') {
              const toolCallResult: ToolCallResult = chunk.result as ToolCallResult;
              const toolResult = toolCallResult.result as Record<string, unknown>;
              const toolResponse = toolCallResult.response;

              if (!toolResponse.startsWith('ERROR')) {
                current.completeExecution(toolResult, toolResponse);

                subject.next({
                  mode: CompletionStreamModeEnum.REPLACE,
                  data: current.toolResultInCoreMessage,
                  path: 'tool_result',
                  targetId: current.id,
                  targetType: 'CompletionBlock',
                });
              } else {
                current.failExecution(toolResult, toolResponse);

                subject.next({
                  mode: CompletionStreamModeEnum.REPLACE,
                  data: current.toolResultInCoreMessage,
                  path: 'extra.error',
                  targetId: current.id,
                  targetType: 'CompletionBlock',
                });
              }

              this.generatedMessages = blocksToMessages(this.blocks);
              spanMap.get(chunk.toolCallId)?.update({
                input: current.toolArguments,
                output: {
                  result: current.toolResult,
                  response: current.toolResponse,
                },
                statusMessage: current.status,
              });
            }

            return chunk;
          }

          return chunk;
        }),
        finalize(async () => {
          if (this.status === GenerationStatusEnum.GENERATING) {
            await onSuccess();
          }
        }),
      )
      .subscribe();

    return subject.asObservable();
  }

  public processTextResult(result: GenerateTextResult<any, any>) {
    const subject = new Subject<CompletionStreamChunk<any>>();

    // add block
    const block = ContentCompletionBlock.createNew({
      messageId: this.chatMessageId,
      data: result.text,
    });
    this.blocks.push(block);

    // add messages
    this.addGeneratedMessage({
      role: 'assistant',
      content: result.text,
    });

    // emit event
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      data: block.toCompletionStreamChunk(),
      dataType: 'CompletionBlock',
    });

    return subject.asObservable();
  }

  static fromCurrentGeneration(current: Generation): Generation {
    return new Generation({
      model: current.model,
      tools: current.tools,
      toolChoice: current.toolChoice as ChatCompletionToolChoiceOption,
      bizArgs: current.bizArgs,
      modelOptions: current.modelOptions,
      traceMetadata: current.traceMetadata,
      prompt: current.prompt,
      promptMessages: [...current.promptMessages, ...current.generatedMessages],
      prediction: current.prediction,
    });
  }
}
