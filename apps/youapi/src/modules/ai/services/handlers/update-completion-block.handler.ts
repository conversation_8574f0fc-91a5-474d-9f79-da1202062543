/**
 * Update Completion Block Handler - 更新完成块处理器
 * 处理更新完成块的业务逻辑
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/updateCompletionBlock/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/universe/apps/youapi/src/modules/chat/services/handlers/update-tool-block.handler.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ToolCompletionBlock } from '../../domain/models/completion-block.entity';
import { CompletionBlockRepository } from '../../repositories/completion-block.repository';
import { UpdateCompletionBlockCommand } from '../commands/update-completion-block.command';

@Injectable()
@CommandHandler(UpdateCompletionBlockCommand)
export class UpdateCompletionBlockHandler implements ICommandHandler<UpdateCompletionBlockCommand> {
  private readonly logger = new Logger(UpdateCompletionBlockHandler.name);

  constructor(private readonly completionBlockRepository: CompletionBlockRepository) {}

  async execute(command: UpdateCompletionBlockCommand): Promise<void> {
    const { blockId, toolResult, toolResponse, extra } = command;
    const block = (await this.completionBlockRepository.getById(blockId)) as ToolCompletionBlock;
    if (!block) return;

    block.updateExecution(toolResult, toolResponse, extra);
    await this.completionBlockRepository.save(block);
    block.commit();
  }
}
