import { Logger } from '@nestjs/common';
import { LangfuseGeneration } from '@/common/decorators';
import { LLMs } from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { FileDomainService } from '@/domain/file';
import { Directory } from '@/domain/file/types';
import { MinimaxEmotion, MinimaxVoice, YLTTSOptions } from '@/infra/youllm';
import { ModelProviderService } from '../providers/index.service';
import { MinimaxSpeechResponse } from '../providers/minimax/minimax-speech-model';
import { BaseRunner } from './base';

export class SpeechRunner extends BaseRunner {
  protected readonly logger = new Logger(SpeechRunner.name);

  private fileDomainService: FileDomainService;

  constructor() {
    super();
    this.fileDomainService = ApplicationContext.getProvider<FileDomainService>(FileDomainService);
  }

  @LangfuseGeneration({
    name: 'audio-generate',
    captureInput: true,
    captureOutput: false,
  })
  async generateSpeech(prompt: string, params: Partial<YLTTSOptions>) {
    // const space = await this.spaceDomainService.getByUserId(userId);
    // await this.usageRecordDomainService.checkQuota(space, QuotaResourceEnum.TTS);

    const { model = LLMs.SPEECH_02_HD, voice, speed, vol, pitch, emotion, subtitleEnable } = params;
    const audioParams = {
      voice: voice as MinimaxVoice,
      model,
      speed: Number(speed),
      vol: Number(vol),
      pitch: Number(pitch),
      emotion: emotion as MinimaxEmotion,
      subtitleEnable,
    };

    const modelProvider =
      ApplicationContext.getProvider<ModelProviderService>(ModelProviderService);
    const speechModel = modelProvider.getSpeechModel(
      model,
      modelProvider.getProviderForModel(model as LLMs),
    );

    const span = await this.traceService.addSpan({
      name: `call-api-${speechModel.provider}`,
      input: audioParams,
    });
    const result = await speechModel.doGenerate({
      text: prompt,
      outputFormat: 'mp3',
      voice: audioParams.voice,
      speed: audioParams.speed,
      providerOptions: {
        minimax: {
          subtitle_enable: audioParams.subtitleEnable,
          voice_setting: {
            emotion: audioParams.emotion,
            // speed: audioParams.speed,
            // vol: audioParams.vol,
            // pitch: audioParams.pitch,
          },
        },
      },
    });
    span.end({
      output: result,
    });

    const { audio: audioBuffer, response } = result;
    const { subtitle_file: subtitleFile } = (response.body as MinimaxSpeechResponse)?.data || {};
    const extraInfo = (response.body as MinimaxSpeechResponse)?.extra_info;

    // // Record usage in background
    // this.usageRecordDomainService
    //   .createTTSUsageRecord({
    //     space_id: space.id,
    //     user_id: userId,
    //     amount: extraInfo?.usage_characters || prompt.length,
    //     voice,
    //   })
    //   .catch((error) => {
    //     this.logger.error('Failed to record TTS usage', error);
    //   });

    const hash = await this.fileDomainService.uploadStringOrBuffer(audioBuffer as Buffer, {
      visibility: 'public',
      contentType: 'audio/mpeg',
      directory: Directory.GEN_AUDIO,
    });
    const audioUrl = `https://cdn.gooo.ai/${Directory.GEN_AUDIO}/${hash}.mp3`;

    this.traceService.updateGeneration({
      usage: {
        input: 0,
        output: extraInfo?.usage_characters || 0,
        total: extraInfo?.usage_characters || 0,
      },
    });
    return {
      hash,
      audioUrl,
      subtitleFile,
      extraInfo,
    };
  }
}
