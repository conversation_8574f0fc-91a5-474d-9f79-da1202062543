import { LanguageModelV1 } from '@ai-sdk/provider';
import { Logger } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { CoreMessage, generateText } from 'ai';
import { LangfuseGenerationClient } from 'langfuse-core';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import { LLMs, MODEL_DEFINITION, ToolNames } from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { Generation } from '../domain/models/generation.entity';
import { ModelProviderService } from '../providers/index.service';

export interface GenerateOptions {
  useCache?: boolean;
  inheritTrace?: boolean;
}

export class BaseRunner {
  protected readonly logger = new Logger(BaseRunner.name);
  protected generations: Generation[] = [];
  protected currentGeneration: Generation;
  protected commandBus: CommandBus;
  protected traceService: LangfuseTraceService;

  constructor() {
    this.traceService = ApplicationContext.getProvider<LangfuseTraceService>(LangfuseTraceService);
    this.commandBus = ApplicationContext.getProvider<CommandBus>(CommandBus);
  }

  addGeneration(generation: Generation) {
    this.generations.push(generation);
    this.currentGeneration = generation;
    return this;
  }

  get model(): LLMs {
    return this.currentGeneration.model;
  }

  setModel(model: LLMs) {
    this.currentGeneration.model = model;
    return this;
  }

  get temperature(): number {
    return this.currentGeneration.modelOptions.temperature;
  }

  setTemperature(temperature: number) {
    this.currentGeneration.modelOptions.temperature = temperature;
    return this;
  }

  setRegenerate(regenerate: boolean) {
    if (regenerate) {
      this.currentGeneration.modelOptions.temperature = Math.min(
        this.currentGeneration.modelOptions.temperature * 2,
        1,
      );
    }
    return this;
  }

  get tools(): ToolNames[] {
    return this.currentGeneration.tools;
  }

  get toolChoice() {
    return this.currentGeneration.toolChoice;
  }

  get topP(): number {
    return this.currentGeneration.modelOptions.topP || 1;
  }

  get frequencyPenalty(): number {
    return this.currentGeneration.modelOptions.frequencyPenalty || 0;
  }

  get presencePenalty(): number {
    return this.currentGeneration.modelOptions.presencePenalty || 0;
  }

  get modelOptions(): {
    topP?: number;
    temperature?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  } {
    return this.currentGeneration.modelOptions;
  }

  get traceMetadata(): Record<string, string> {
    return this.currentGeneration.traceMetadata;
  }

  updateMetadata(metadata: Record<string, string>) {
    this.currentGeneration.traceMetadata = {
      ...this.currentGeneration.traceMetadata,
      ...metadata,
    };
    return this;
  }

  get promptName(): string {
    return this.currentGeneration.promptName;
  }

  get promptMessages(): CoreMessage[] {
    return this.currentGeneration.promptMessages;
  }

  protected getModel(useCache: boolean): LanguageModelV1 {
    return ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache });
  }

  protected async startTrace(
    model: LanguageModelV1,
    name?: string,
    options?: GenerateOptions,
  ): Promise<LangfuseGenerationClient> {
    const generation = await this.traceService.addGeneration(
      {
        ...this.buildGenerationParams(model, name || this.promptName),
        metadata: {
          ...this.traceMetadata,
          useCache: options?.useCache,
        },
      },
      options?.inheritTrace,
    );
    return generation;
  }

  /**
   * Common method to assemble generation parameters for tracing
   */
  protected buildGenerationParams(model: LanguageModelV1, name?: string) {
    const input = this.currentGeneration?.promptMessages || this.currentGeneration?.bizArgs;
    const params = {
      name: name || this.promptName || 'text-generation',
      model: model.modelId,
      input: input,
      modelParameters: {
        provider: model.provider,
        ...this.modelOptions,
        ...(this.tools.length > 0 && {
          tools: this.tools.join(', '),
          toolChoice: typeof this.toolChoice === 'string' ? this.toolChoice : 'required',
        }),
      },
    };

    return params;
  }

  /**
   * Common method to extract and format usage information for tracing
   */
  protected buildUsageReport(usage: any) {
    if (!usage) return undefined;

    // Calculate cost details if model definition is available
    const modelDef = MODEL_DEFINITION.get(this.model);
    let costDetails: any;
    let usageInfo: any;
    let usageDetails: any;

    if (modelDef) {
      costDetails = {
        input:
          (usage.promptTokens / 1000000) * modelDef.input_per_mil +
          ((usage.cachedInputTokens || 0) / 1000000) * modelDef.cached_input_per_mil,
        output:
          ((usage.completionTokens + (usage.reasoningTokens || 0)) / 1000000) *
          modelDef.output_per_mil,
        cache_read_input_tokens: usage.cachedInputTokens || 0,
        total: 0,
      };
      costDetails.total = costDetails.input + costDetails.output;
    }

    usageInfo = {
      promptTokens: usage.promptTokens || usage.inputTokens,
      completionTokens: usage.completionTokens || usage.outputTokens,
      totalTokens: usage.totalTokens,
    };

    usageDetails = {
      input: usage.promptTokens || usage.inputTokens,
      output: usage.completionTokens || usage.outputTokens,
      input_audio_tokens: 0,
      input_cache_tokens: usage.cachedInputTokens || 0,
      output_audio_tokens: 0,
      output_reasoning_tokens: usage.reasoningTokens || 0,
      output_accepted_prediction_tokens: 0,
      output_rejected_prediction_tokens: 0,
    };

    return {
      usageInfo,
      usageDetails,
      ...(costDetails && { costDetails }),
    };
  }

  protected assembleApiParameters(model: LanguageModelV1): Parameters<typeof generateText>[0] {
    if (!this.promptMessages?.length) {
      throw new Error('Prompt messages are required');
    }
    const params: Parameters<typeof generateText>[0] = {
      model,
      messages: this.promptMessages,
      ...this.modelOptions,
      // 先注释掉，目前看来只有 Chat 会用到 tool
      // ...(this.tools && this.tools.length > 0
      //   ? {
      //       tools: this.tools.reduce(
      //         (acc, toolName: ToolNames) => {
      //           acc[toolName] = {
      //             // TODO: @jialiang 提供方法通过工具名获得工具定义
      //             parameters: z.object({
      //               query: z.string().describe('Tool input parameters'),
      //             }),
      //             description: `Execute ${toolName} tool`,
      //             execute: async (input, { toolCallId }) => {
      //               return await this.commandBus.execute(
      //                 new CallToolCommand({
      //                   input,
      //                   generation: this.currentGeneration,
      //                   completionBlock: ToolCompletionBlock.createNew({
      //                     messageId: '',
      //                     toolId: toolCallId,
      //                     toolName: toolName,
      //                     toolArguments: input,
      //                     toolResult: {},
      //                     toolResponse: '',
      //                   }),
      //                 }),
      //               );
      //             },
      //           } as Tool;
      //           return acc;
      //         },
      //         {} as Record<string, Tool>,
      //       ),
      //       toolChoice: typeof this.toolChoice === 'string' ? this.toolChoice : {
      //         type: 'tool',
      //         toolName: this.toolChoice.function.name,
      //       },
      //     }
      //   : {}),
    };
    const modelDefinition = MODEL_DEFINITION.get(this.model);
    if (modelDefinition.type === 'reasoning') {
      delete params.temperature;
    }

    // 模型参数修改
    if (model.modelId === LLMs.GPT_4O_MINI) {
      params.presencePenalty = 0.8;
      params.frequencyPenalty = 0.8;
    }
    if (!params.providerOptions) {
      params.providerOptions = {};
    }

    if (modelDefinition.from === 'openai') {
      if (!params.providerOptions.openai) {
        params.providerOptions.openai = {};
      }

      params.providerOptions.openai.parallelToolCalls = false;
      if (modelDefinition.type === 'reasoning') {
        params.providerOptions.openai.reasoningEffort = 'medium';
      }

      if (
        [LLMs.GPT_4O_MINI, LLMs.GPT_4O].includes(this.model) &&
        this.currentGeneration.prediction.length > 0
      ) {
        params.providerOptions.openai.prediction = {
          type: 'content',
          content: this.currentGeneration.prediction.map((p) => ({ type: 'text', text: p })),
        };
      }
    }
    if (modelDefinition.from === 'anthropic') {
      if (!params.providerOptions.anthropic) {
        params.providerOptions.anthropic = {};
      }

      if (modelDefinition.type === 'reasoning') {
        params.providerOptions.anthropic.thinking = {
          type: 'enabled',
          budgetTokens: modelDefinition.output_token_limit / 2,
        };
      }
    }

    // 加接口失败重试
    params.maxRetries = 2;

    return params;
  }
}
