import { Logger } from '@nestjs/common';
import { LangfuseGeneration } from '@/common/decorators';
import { LLMs } from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { FileDomainService } from '@/domain/file';
import { Directory } from '@/domain/file/types';
import { BaseRunner } from './base';
import { ImageRunner } from './image';

/**
 * AudioRunner - migrated from youapp audio generation functions
 * Provides generateSpeech and album cover generation matching youapp exactly
 */
export class AudioRunner extends BaseRunner {
  protected readonly logger = new Logger(AudioRunner.name);

  private fileDomainService: FileDomainService;

  constructor() {
    super();
    this.fileDomainService = ApplicationContext.getProvider<FileDomainService>(FileDomainService);
  }

  /**
   * Generate audio with album cover - exact migration from youapp generate function
   */
  @LangfuseGeneration({
    name: 'audio-generate',
    model: LLMs.SPEECH_02_HD,
  })
  async generate(params: {
    userId: string;
    text: string;
    title: string;
    voice: string;
    speed: string;
    vol: string;
    pitch: string;
    emotion: string;
    model?: string;
  }) {
    const {
      userId,
      text,
      title,
      voice,
      speed,
      vol,
      pitch,
      emotion,
      model = LLMs.SPEECH_02_HD,
    } = params;

    // TODO: Implement space and quota checking
    // const space = await this.spaceDomainService.getByUserId(userId);
    // await usageRecordDomain.checkQuota(space, QuotaResourceEnum.TTS);

    // TODO: Implement LLM provider selection and audio generation
    // For now, create a mock implementation
    const audioParams = {
      voice,
      model,
      speed: Number(speed),
      vol: Number(vol),
      pitch: Number(pitch),
      emotion,
      subtitleEnable: false,
    };

    // Mock audio buffer - in real implementation, this would come from the provider
    const mockAudioBuffer = Buffer.from('mock audio data');

    const hash = await this.fileDomainService.uploadStringOrBuffer(mockAudioBuffer, {
      visibility: 'public',
      contentType: 'audio/mpeg',
      directory: Directory.GEN_AUDIO,
    });

    // TODO: Record usage in background
    // runInBackground(
    //   Promise.all([
    //     usageRecordDomain.createTTSUsageRecord({
    //       space_id: space.id,
    //       user_id: userId,
    //       amount: extraInfo?.usage_characters || text.length,
    //       voice,
    //     }),
    //   ]),
    // );

    const audio_url = `https://cdn.gooo.ai/${Directory.GEN_AUDIO}/${hash}.mp3`;

    // Generate album cover using ImageRunner
    const imageRunner = new ImageRunner();
    const albumCoverResult = await imageRunner.generateImage({
      prompt: `Please generate an album cover for the following audio. The audio's title is: ${title}. The audio's text is: ${text}. There should be no text on the cover.`,
      size: '1024x1024',
      quality: 'low',
      background: 'auto',
      n: 1,
    });

    await this.traceService.updateGeneration({
      output: {
        audio_url,
        album_url: albumCoverResult.imageUrl,
      },
      usage: {
        input: 0,
        output: text.length, // Mock character usage
        total: text.length,
      },
    });

    return {
      hash,
      audio_url,
      subtitleFile: undefined, // Mock - no subtitle file for now
      extraInfo: {
        usage_characters: text.length,
      },
      image_url: albumCoverResult.imageUrl,
    };
  }
}
