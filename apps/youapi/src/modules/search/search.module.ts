import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { DomainModule } from '@/domain/domain.module';
import { InfraModule } from '@/infra/infra.module';
import { IamModule } from '@/modules/iam/iam.module';
import { SearchController } from './controllers/search.controller';
import { ExternalSearchService } from './services/external-search.service';
import { InternalSearchService } from './services/internal-search.service';
import { SearchService } from './services/search.service';
import { WebSearchService } from './services/web-search.service';

/**
 * 搜索模块
 * 提供搜索相关的HTTP API端点和业务逻辑
 *
 * 架构说明:
 * - Controller: 处理HTTP请求和响应
 * - Service: 业务逻辑层，包装SearchDomainService
 * - Domain: 使用现有的SearchDomainService和相关基础设施
 *
 * Migrated from:
 * - youapp/src/app/api/v1/search/route.ts
 * - youapp/src/lib/app/search/index.ts
 */
@Module({
  imports: [
    CqrsModule, // CQRS 支持（未来可能用于复杂搜索命令）
    DomainModule, // 获取 SearchDomainService
    InfraModule, // 获取 Brave/Bocha/Jina 等基础设施服务
    IamModule, // 用户认证和权限管理
  ],
  controllers: [SearchController],
  providers: [SearchService, WebSearchService, InternalSearchService, ExternalSearchService],
  exports: [
    SearchService, // 导出服务供其他模块使用
    WebSearchService, // 导出 Web 搜索服务供 AI 模块使用
  ],
})
export class SearchModule {}
