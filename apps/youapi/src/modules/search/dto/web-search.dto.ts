/**
 * Web Search DTOs and Types
 * Migrated from youapp web search domain
 */

export interface WebSearchParams {
  query: string;
  type?: 'webpage' | 'video' | 'events' | 'scholar';
  location?: string;
  event_filter?: string;
  language?: string;
  multilingual_queries?: { q: string; lang: string }[];
  freshness?: string;
  chat: any; // ChatDetail - will be replaced with proper type
  limit?: number;
}

export enum InternetSearchResultTypeEnum {
  WEBPAGE = 'webpage',
  VIDEO = 'video',
  EVENT = 'event',
  SCHOLAR = 'scholar',
  APP = 'app',
}

export interface InternetSearchResult {
  url: string;
  title: string;
  favicon?: string;
  site_name?: string;
  related_chunk?: string;
  time?: string;
  author?: string;
  address?: string;
  type?: InternetSearchResultTypeEnum;
  images?: Array<{ caption: string; url: string }>;
  extra?: any;
}

export interface WebSearchResponse {
  results: InternetSearchResult[];
  total: number;
  query: string;
  reranked?: boolean;
}

export interface InternalSearchResult {
  url: string;
  title: string;
  content?: string;
  site_name?: string;
  related_chunk?: string;
  type: InternetSearchResultTypeEnum;
  extra?: {
    snip_id?: string;
    board_id?: string;
    created_at?: string;
    [key: string]: any;
  };
}

export interface JinaRerankRequest {
  model: string;
  query: string;
  documents: Array<{
    text: string;
    url?: string;
    title?: string;
  }>;
  top_n?: number;
  return_documents?: boolean;
}

export interface JinaRerankResult {
  index: number;
  relevance_score: number;
  document?: {
    text: string;
    url?: string;
    title?: string;
  };
}

export interface JinaRerankResponse {
  model: string;
  usage: {
    total_tokens: number;
    prompt_tokens: number;
  };
  results: JinaRerankResult[];
}
