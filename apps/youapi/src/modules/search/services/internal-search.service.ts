/**
 * Internal Search Service
 * Migrated from youapp InternalSearchDomainService
 *
 * NOTE: This is a temporary implementation using search service.
 * The original youapp implementation uses context builder approach:
 * 1. getGlobalMaterialsLibraryMeta(userId) - get materials library board
 * 2. createContextBuilder() - create context builder with analysis parameters
 * 3. buildContextFromReferences() - build context from board references
 * 4. Extract snip chunks from context results
 * 5. snipDomain.listBriefSnipsByIds() - get full snip details
 * 6. Transform to InternetSearchResult format
 *
 * TODO: Update to use context builder approach once it's migrated to youapi
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import {
  InternetSearchResult,
  InternetSearchResultTypeEnum,
  WebSearchParams,
} from '../dto/web-search.dto';
import { SearchService } from './search.service';

@Injectable()
export class InternalSearchService {
  private readonly logger = new Logger(InternalSearchService.name);

  // Default search fields for internal content
  private readonly SEARCH_FIELDS = [
    'snips.title',
    'snips.content',
    'snips.authors',
    'snips.webpage.url',
    'snips.description',
    'snips.show_notes',
    'snips.transcript',
    'snips.overview',
    'snips.extracted_text',
    'thoughts.title',
    'thoughts.content',
  ];

  constructor(private readonly searchService: SearchService) {}

  /**
   * Search internal content library
   * Migrated from youapp InternalSearchDomainService.search()
   */
  async search(
    params: WebSearchParams,
    userId: string,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'internal-content-search',
      input: {
        query: params.query,
        userId,
        language: params.language,
      },
    });

    try {
      // Skip internal search if query language is not Chinese for now
      // This matches the original youapp behavior
      if (params.language && !this.isChinese(params.language)) {
        this.logger.log('Skipping internal search for non-Chinese query');
        childSpan?.end({ output: { count: 0, reason: 'language_filter' } });
        return [];
      }

      // TODO: Implement proper context builder approach when it's migrated
      // For now, fallback to search service until context builder is available
      const searchResult = await this.searchService.hybridSearch(params.query, userId, undefined);

      // Transform search results to InternetSearchResult format
      const results = this.transformToWebSearchResults(searchResult);

      this.logger.log(`Found ${results.length} internal search results`);

      childSpan?.end({
        output: {
          count: results.length,
          hasResults: results.length > 0,
        },
      });

      return results;
    } catch (error) {
      this.logger.error('Internal search failed', error);
      childSpan?.end({
        level: 'ERROR',
        statusMessage: error.message,
      });
      return [];
    }
  }

  /**
   * Transform internal search results to web search format
   * Migrated from youapp result transformation logic
   */
  private transformToWebSearchResults(searchResult: any): InternetSearchResult[] {
    if (!searchResult?.hits) {
      return [];
    }

    const results: InternetSearchResult[] = [];

    for (const hit of searchResult.hits) {
      try {
        const doc = hit.document;
        if (!doc) continue;

        // Create URL based on content type
        let url = '';
        let type = InternetSearchResultTypeEnum.WEBPAGE;

        if (doc.type === 'snip' && doc.id) {
          url = `https://gooo.ai/s/${doc.id}`;
          type = this.getSnipType(doc);
        } else if (doc.type === 'thought' && doc.id) {
          url = `https://gooo.ai/t/${doc.id}`;
          type = InternetSearchResultTypeEnum.WEBPAGE;
        } else {
          continue; // Skip if we can't create a valid URL
        }

        // Extract relevant chunk content
        const relatedChunk = this.extractRelatedChunk(hit);

        const result: InternetSearchResult = {
          url,
          title: doc.title || 'Untitled',
          site_name: 'YouMind Library',
          related_chunk: relatedChunk,
          type,
          time: doc.created_at,
          extra: {
            snip_id: doc.type === 'snip' ? doc.id : undefined,
            thought_id: doc.type === 'thought' ? doc.id : undefined,
            board_id: doc.board_id,
            created_at: doc.created_at,
            score: hit.text_match_info?.score,
            internal: true, // Mark as internal result
          },
        };

        // Add favicon for internal results
        result.favicon = 'https://gooo.ai/favicon.ico';

        results.push(result);
      } catch (error) {
        this.logger.warn('Failed to transform search result', { error, hit });
      }
    }

    return results;
  }

  /**
   * Extract relevant chunk from search hit
   * Migrated from youapp chunk extraction logic
   */
  private extractRelatedChunk(hit: any): string | undefined {
    // Try to get highlighted content first
    if (hit.highlights && hit.highlights.length > 0) {
      const highlight = hit.highlights[0];
      if (highlight.snippet) {
        return this.sanitizeText(highlight.snippet);
      }
    }

    // Fallback to document content
    const doc = hit.document;
    if (doc.content) {
      // Take first 200 characters as chunk
      const content = this.sanitizeText(doc.content);
      return content.length > 200 ? content.substring(0, 200) + '...' : content;
    }

    return undefined;
  }

  /**
   * Determine snip type based on content
   * Migrated from youapp type detection
   */
  private getSnipType(doc: any): InternetSearchResultTypeEnum {
    // Check if it's a video based on metadata
    if (doc.video_url || doc.transcript) {
      return InternetSearchResultTypeEnum.VIDEO;
    }

    // Default to webpage
    return InternetSearchResultTypeEnum.WEBPAGE;
  }

  /**
   * Check if language is Chinese
   * Migrated from youapp language detection
   */
  private isChinese(language: string): boolean {
    return language.toLowerCase().includes('zh') || language.toLowerCase().includes('chinese');
  }

  /**
   * Sanitize control characters from text
   * Migrated from youapp sanitization utility
   */
  private sanitizeText(text: string): string {
    if (!text) return '';

    // Remove control characters except newlines and tabs
    return text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  }
}
