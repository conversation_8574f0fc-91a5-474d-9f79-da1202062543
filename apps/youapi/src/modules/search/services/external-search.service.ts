/**
 * External Search Service
 * Migrated from youapp ExternalSearchDomainService and InternetSearchDomainService
 *
 * <PERSON>les searching external internet sources using various search providers
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LangfuseSpanClient } from 'langfuse-core';
import { BochaSearchService } from '@/infra/bocha';
import { BraveSearchService } from '@/infra/brave';
import { GoogleEventsService } from '@/infra/serp/google-events.service';
import { GoogleScholarService } from '@/infra/serp/google-scholar.service';
import { GoogleSearchService } from '@/infra/serp/google-search.service';
import { YouTubeSearchService } from '@/infra/serp/youtube.service';
import {
  InternetSearchResult,
  InternetSearchResultTypeEnum,
  WebSearchParams,
} from '../dto/web-search.dto';

@Injectable()
export class ExternalSearchService {
  private readonly logger = new Logger(ExternalSearchService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly braveSearchService: BraveSearchService,
    private readonly bochaSearchService: BochaSearchService,
    private readonly googleSearchService: GoogleSearchService,
    private readonly youtubeSearchService: YouTubeSearchService,
    private readonly googleScholarService: GoogleScholarService,
    private readonly googleEventsService: GoogleEventsService,
  ) {}

  /**
   * Search external internet sources
   * Migrated from youapp ExternalSearchDomainService.search()
   */
  async search(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'external-internet-search',
      input: {
        query: params.query,
        type: params.type,
        language: params.language,
      },
    });

    try {
      let results: InternetSearchResult[] = [];

      // Route to appropriate search method based on type
      switch (params.type) {
        case 'video':
          results = await this.searchVideos(params, childSpan);
          break;
        case 'scholar':
          results = await this.searchScholar(params, childSpan);
          break;
        case 'events':
          results = await this.searchEvents(params, childSpan);
          break;
        case 'webpage':
        default:
          results = await this.searchWebpages(params, childSpan);
          break;
      }

      // Filter out invalid results
      results = await this.filterValidResults(results);

      this.logger.log(`Found ${results.length} external search results`);

      childSpan?.end({
        output: {
          count: results.length,
          type: params.type || 'webpage',
        },
      });

      return results;
    } catch (error) {
      this.logger.error('External search failed', error);
      childSpan?.end({
        level: 'ERROR',
        statusMessage: error.message,
      });
      return [];
    }
  }

  /**
   * Search web pages using multiple providers
   * Migrated from youapp aggregate search logic
   */
  private async searchWebpages(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'webpage-search',
      input: { query: params.query, language: params.language },
    });

    try {
      // Determine search provider based on language
      const isChineseQuery = this.detectChinese(params.query);

      let results: InternetSearchResult[] = [];

      if (isChineseQuery) {
        // Use Bocha search for Chinese queries
        results = await this.searchWithBocha(params, childSpan);
      } else {
        // Use Brave/Google for English queries
        results =
          (await this.searchWithBrave(params, childSpan)) ||
          (await this.searchWithGoogle(params, childSpan));
      }

      childSpan?.end({ output: { count: results.length } });
      return results;
    } catch (error) {
      this.logger.error('Webpage search failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      return [];
    }
  }

  /**
   * Search videos using YouTube SERP API
   * Uses searchYouTube function from infra
   */
  private async searchVideos(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'video-search',
      input: { query: params.query },
    });

    try {
      const youtubeResults = await this.youtubeSearchService.search({
        search_query: params.query,
        num: params.limit || 10,
        hl: this.langCodeToCSEHL(params.language || 'en'),
      });

      const results = youtubeResults.map((result) => this.transformYouTubeResult(result));

      childSpan?.end({ output: { count: results.length } });
      return results;
    } catch (error) {
      this.logger.error('Video search failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      return [];
    }
  }

  /**
   * Search scholarly articles using Google Scholar SERP API
   * Uses searchGoogleScholar function from infra
   */
  private async searchScholar(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'scholar-search',
      input: { query: params.query },
    });

    try {
      const scholarResults = await this.googleScholarService.search({
        q: params.query,
        num: params.limit || 10,
        hl: this.langCodeToCSEHL(params.language || 'en'),
      });

      const results = scholarResults.map((result) => this.transformScholarResult(result));

      childSpan?.end({ output: { count: results.length } });
      return results;
    } catch (error) {
      this.logger.error('Scholar search failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      return [];
    }
  }

  /**
   * Search events using Google Events SERP API
   * Uses searchGoogleEvents function from infra
   */
  private async searchEvents(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'events-search',
      input: { query: params.query, location: params.location },
    });

    try {
      const eventsResults = await this.googleEventsService.search({
        q: params.query,
        location: params.location,
        hl: this.langCodeToCSEHL(params.language || 'en'),
        htichips: params.event_filter,
      });

      const results = eventsResults.map((result) => this.transformEventsResult(result));

      childSpan?.end({ output: { count: results.length } });
      return results;
    } catch (error) {
      this.logger.error('Events search failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      return [];
    }
  }

  /**
   * Search with Brave Search API
   * Uses existing BraveSearchService from infra
   */
  private async searchWithBrave(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'brave-search',
      input: { query: params.query },
    });

    try {
      if (!this.braveSearchService.isConfigured()) {
        this.logger.warn('Brave search not configured');
        childSpan?.end({ output: { count: 0, reason: 'not_configured' } });
        return [];
      }

      const response = await this.braveSearchService.webSearch(params.query, {
        maxResults: String(params.limit || 10),
        freshness: params.freshness,
      });

      const webResults = this.braveSearchService.getWebResults(response);
      const results = webResults.map((result) => this.transformBraveResult(result));

      childSpan?.end({ output: { count: results.length } });
      return results;
    } catch (error) {
      this.logger.error('Brave search failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      return [];
    }
  }

  /**
   * Search with Google SERP API
   * Uses searchGoogle function from infra
   */
  private async searchWithGoogle(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'google-search',
      input: { query: params.query },
    });

    try {
      const googleResults = await this.googleSearchService.search({
        q: params.query,
        num: params.limit || 10,
        hl: this.langCodeToCSEHL(params.language || 'en'),
      });

      const results = googleResults.map((result) => this.transformGoogleResult(result));

      childSpan?.end({ output: { count: results.length } });
      return results;
    } catch (error) {
      this.logger.error('Google search failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      return [];
    }
  }

  /**
   * Search with Bocha (Chinese search engine)
   * Uses existing BochaSearchService from infra
   */
  private async searchWithBocha(
    params: WebSearchParams,
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'bocha-search',
      input: { query: params.query },
    });

    try {
      if (!this.bochaSearchService.isConfigured()) {
        this.logger.warn('Bocha search not configured');
        childSpan?.end({ output: { count: 0, reason: 'not_configured' } });
        return [];
      }

      const bochaResults = await this.bochaSearchService.search({
        query: params.query,
        freshness: params.freshness,
        count: params.limit || 10,
        summary: true,
      });

      const results = bochaResults.map((result) => this.transformBochaResult(result));

      childSpan?.end({ output: { count: results.length } });
      return results;
    } catch (error) {
      this.logger.error('Bocha search failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      return [];
    }
  }

  /**
   * Filter out invalid/404 results
   * Migrated from youapp URL validation
   */
  private async filterValidResults(
    results: InternetSearchResult[],
  ): Promise<InternetSearchResult[]> {
    // For now, just return all results
    // In the future, could add URL validation checks
    return results.filter((result) => {
      // Basic validation
      return result.url && result.title && this.isValidUrl(result.url);
    });
  }

  /**
   * Extract hostname from URL
   */
  private getHostname(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return '';
    }
  }

  /**
   * Generate a favicon URL for a given URL
   */
  private getFaviconUrl(url: string): string {
    const hostname = this.getHostname(url || '');
    return hostname ? `http://www.google.com/s2/favicons?domain=${hostname}&sz=64` : '';
  }

  /**
   * Convert language code to CSE HL format
   * Migrated from youapp language conversion
   */
  private langCodeToCSEHL(lang: string): string {
    switch (lang) {
      case 'zh':
        return 'zh-CN';
      case 'zh-Hant':
        return 'zh-TW';
      default:
        return lang;
    }
  }

  /**
   * Detect if query contains Chinese characters
   * Migrated from youapp language detection
   */
  private detectChinese(query: string): boolean {
    const chineseRegex = /[\u4e00-\u9fff]/;
    return chineseRegex.test(query);
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Transform Google search result to InternetSearchResult format
   */
  private transformGoogleResult(result: any): InternetSearchResult {
    return {
      url: result.link || '',
      title: this.sanitizeText(result.title),
      site_name: result.source || this.getHostname(result.link || ''),
      favicon: this.getFaviconUrl(result.link || ''),
      related_chunk: this.sanitizeText(result.snippet || ''),
      type: InternetSearchResultTypeEnum.WEBPAGE,
      time: result.date,
      extra: {
        google_result: true,
        position: result.position,
      },
    };
  }

  /**
   * Transform Brave search result to InternetSearchResult format
   */
  private transformBraveResult(result: any): InternetSearchResult {
    return {
      url: result.url,
      title: this.sanitizeText(result.title),
      site_name: result.meta_url?.hostname,
      favicon: result.meta_url?.favicon,
      related_chunk: this.sanitizeText(result.description),
      type: InternetSearchResultTypeEnum.WEBPAGE,
      time: result.age,
      extra: {
        brave_result: true,
        language: result.language,
      },
    };
  }

  /**
   * Transform Bocha search result to InternetSearchResult format
   */
  private transformBochaResult(result: any): InternetSearchResult {
    return {
      url: result.url,
      title: this.sanitizeText(result.name),
      site_name: result.siteName,
      favicon: result.siteIcon,
      related_chunk: this.sanitizeText(result.snippet || result.summary),
      type: InternetSearchResultTypeEnum.WEBPAGE,
      time: result.dateLastCrawled,
      extra: {
        bocha_result: true,
        cached_url: result.cachedPageUrl,
        language: result.language,
      },
    };
  }

  /**
   * Transform YouTube search result to InternetSearchResult format
   */
  private transformYouTubeResult(result: any): InternetSearchResult {
    return {
      url: result.link || '',
      title: this.sanitizeText(result.title),
      site_name: 'YouTube',
      favicon: 'https://www.youtube.com/favicon.ico',
      related_chunk: this.sanitizeText(result.description),
      type: InternetSearchResultTypeEnum.VIDEO,
      time: result.published_date,
      author: result.channel?.name,
      images: result.thumbnail?.static
        ? [
            {
              caption: result.title,
              url: result.thumbnail.static,
            },
          ]
        : undefined,
      extra: {
        youtube_result: true,
        duration: result.length,
        views: result.views,
        channel: result.channel?.name,
        position: result.position_on_page,
      },
    };
  }

  /**
   * Transform Google Scholar result to InternetSearchResult format
   */
  private transformScholarResult(result: any): InternetSearchResult {
    return {
      url: result.link || '',
      title: this.sanitizeText(result.title),
      site_name: 'Google Scholar',
      related_chunk: this.sanitizeText(result.snippet || ''),
      type: InternetSearchResultTypeEnum.SCHOLAR,
      author: result.publication_info?.summary,
      extra: {
        scholar_result: true,
        position: result.position,
        citations: result.inline_links?.cited_by?.total,
        publication_info: result.publication_info?.summary,
        resources: result.resources,
      },
    };
  }

  /**
   * Transform Google Events result to InternetSearchResult format
   */
  private transformEventsResult(result: any): InternetSearchResult {
    return {
      url: result.link || '',
      title: this.sanitizeText(result.title),
      site_name: result.venue?.name || 'Events',
      related_chunk: this.sanitizeText(result.description || ''),
      type: InternetSearchResultTypeEnum.EVENT,
      time: result.date?.start_date + ' ' + result.date?.when,
      address: Array.isArray(result.address) ? result.address.join(', ') : result.address,
      images: result.thumbnail
        ? [
            {
              caption: result.title,
              url: result.thumbnail,
            },
          ]
        : undefined,
      extra: {
        event_result: true,
        venue: result.venue,
        date_info: result.date,
        ticket_info: result.ticket_info,
        event_location_map: result.event_location_map,
      },
    };
  }

  /**
   * Sanitize control characters from text
   * Migrated from youapp sanitization utility
   */
  private sanitizeText(text: string): string {
    if (!text) return '';

    // Remove control characters except newlines and tabs
    return text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  }
}
