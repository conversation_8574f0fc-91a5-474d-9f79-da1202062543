/**
 * Web Search Service
 * Migrated from youapp WebSearchDomainService
 *
 * Provides hybrid web search functionality combining:
 * - Internal content library search
 * - External internet search
 * - Result reranking using Jina AI
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LangfuseSpanClient } from 'langfuse-core';
import { RerankParam, rerankDocuments } from '@/infra/jina/rerank';
import { InternetSearchResult, WebSearchParams, WebSearchResponse } from '../dto/web-search.dto';
import { ExternalSearchService } from './external-search.service';
import { InternalSearchService } from './internal-search.service';

@Injectable()
export class WebSearchService {
  private readonly logger = new Logger(WebSearchService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly internalSearchService: InternalSearchService,
    private readonly externalSearchService: ExternalSearchService,
  ) {}

  /**
   * Main search method - orchestrates hybrid search
   * Migrated from youapp WebSearchDomainService.search()
   */
  async search(params: WebSearchParams, span?: LangfuseSpanClient): Promise<WebSearchResponse> {
    this.logger.log(`Web search for query: "${params.query}"`);

    try {
      // Run internal and external search in parallel
      const [internalResults, externalResults] = await Promise.allSettled([
        this.internalSearchService.search(params, params.chat?.user_id, span),
        this.externalSearchService.search(params, span),
      ]);

      // Merge results
      const allResults = this.mergeResults(
        internalResults.status === 'fulfilled' ? internalResults.value : [],
        externalResults.status === 'fulfilled' ? externalResults.value : [],
      );

      // Rerank results if we have enough
      let finalResults = allResults;
      let reranked = false;

      if (allResults.length > 1 && this.shouldRerank(params)) {
        try {
          finalResults = await this.rerankResults(params.query, allResults, span);
          reranked = true;
        } catch (error) {
          this.logger.warn('Reranking failed, using original results', error);
        }
      }

      // Apply limit
      const limit = params.limit || 15;
      const limitedResults = finalResults.slice(0, limit);

      return {
        results: limitedResults,
        total: limitedResults.length,
        query: params.query,
        reranked,
      };
    } catch (error) {
      this.logger.error('Web search failed', error);
      throw error;
    }
  }

  /**
   * Merge and deduplicate search results
   * Migrated from WebSearchDomainService.mergeResults()
   */
  private mergeResults(
    internalResults: InternetSearchResult[],
    externalResults: InternetSearchResult[],
  ): InternetSearchResult[] {
    const urlSet = new Set<string>();
    const mergedResults: InternetSearchResult[] = [];

    // Add internal results first (they get priority)
    for (const result of internalResults) {
      if (!urlSet.has(result.url)) {
        urlSet.add(result.url);
        mergedResults.push(result);
      }
    }

    // Add external results
    for (const result of externalResults) {
      if (!urlSet.has(result.url)) {
        urlSet.add(result.url);
        mergedResults.push(result);
      }
    }

    return mergedResults;
  }

  /**
   * Rerank results using Jina AI
   * Uses existing rerankDocuments from infra/jina
   */
  private async rerankResults(
    query: string,
    results: InternetSearchResult[],
    span?: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const childSpan = span?.span({
      name: 'jina-rerank',
      input: { query, count: results.length },
    });

    try {
      // Prepare documents for reranking
      const documents = results.map((result) =>
        `${result.title} ${result.related_chunk || ''}`.trim(),
      );

      const rerankParam: RerankParam = {
        model: 'jina-reranker-v2-base-multilingual',
        query,
        documents,
        top_n: Math.min(results.length, 15),
        timeout: 10000,
      };

      // Use existing Jina rerank service
      const jinaResponse = await rerankDocuments(rerankParam);

      if (!jinaResponse || !jinaResponse.results) {
        throw new Error('Jina rerank service returned no results');
      }

      // Filter results with score >= 0.5 and map back to original results
      const rerankedResults: InternetSearchResult[] = [];

      for (const result of jinaResponse.results) {
        if (result.relevance_score >= 0.5 && result.index < results.length) {
          rerankedResults.push(results[result.index]);
        }
      }

      this.logger.log(`Reranked ${results.length} -> ${rerankedResults.length} results`);

      childSpan?.end({
        output: {
          originalCount: results.length,
          rerankedCount: rerankedResults.length,
          usage: jinaResponse.usage,
        },
      });

      return rerankedResults;
    } catch (error) {
      this.logger.error('Reranking failed', error);
      childSpan?.end({ level: 'ERROR', statusMessage: error.message });
      throw error;
    }
  }

  /**
   * Determine if results should be reranked
   */
  private shouldRerank(params: WebSearchParams): boolean {
    // Skip reranking for specific search types that don't benefit from it
    return params.type !== 'events' && params.type !== 'video';
  }

  /**
   * Sanitize control characters from text
   * Migrated from youapp sanitizeControlCharacters utility
   */
  private sanitizeText(text: string): string {
    if (!text) return '';

    // Remove control characters except newlines and tabs
    return text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  }

  /**
   * Check if URL returns 404
   * Migrated from youapp URL validation logic
   */
  private async isUrlValid(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}
