import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { DISCRIMINATOR_DEFAULT_NAME } from '../base/swagger-base-dto';

/**
 * Swagger 类名 discriminator 拦截器，用于在 Swagger 中添加默认的 discriminator 属性，用于区分不同的 DTO 类型
 */
@Injectable()
export class SwaggerClassNameDiscriminatorInterceptor implements NestInterceptor {
  intercept(_context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        if (data && typeof data === 'object') {
          return this.addTypeInfo(data);
        }
        return data;
      }),
    );
  }

  private addTypeInfo(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map((item) => this.addTypeInfo(item));
    }

    if (obj && typeof obj === 'object' && obj.constructor && obj.constructor.name) {
      // 避免重复添加 $class 属性
      if (obj[DISCRIMINATOR_DEFAULT_NAME]) {
        return obj;
      }

      return {
        ...obj,
        [DISCRIMINATOR_DEFAULT_NAME]: obj.constructor.name,
      };
    }

    return obj;
  }
}
