/**
 * Tool Types - 工具类型定义
 *
 * 重新设计的工具系统类型定义，基于现有的 domain/chat/tool_call 架构
 */

import { ToolCompletionBlock } from '@/modules/ai/domain/models/completion-block.entity';
import { Chat } from '@/modules/chat/domain/chat/models/chat.entity';

/**
 * 工具函数参数（兼容性别名）
 */
export interface ToolFunctionParameters {
  chat: Chat;
  userId: string;
  parsedParams: Record<string, string>;
  completionBlock: ToolCompletionBlock;
}

/**
 * 工具调用结果
 */
export interface ToolCallResult {
  // 给 LLM 的调用结果，
  response: string;
  // Tool Call 的中间结果
  result?: Record<string, unknown>;
  // 其他参数
  extra?: Record<string, unknown>;
}
