/**
 * 规定的 discriminator 属性名
 */
export const DISCRIMINATOR_DEFAULT_NAME = '$class';

/**
 * Swagger 基类，为所有继承的 DTO 类提供 discriminator 属性。使用 oneOf 的 dto 最好需要继承这个类
 * 每个实例都会有实际的 $class 属性，用于类型判断
 */
export class SwaggerBaseDto {
  /**
   * Discriminator 属性，用于标识具体的类型
   */
  [DISCRIMINATOR_DEFAULT_NAME]: string;

  constructor() {
    // 创建一个实际的属性来存储类名
    this[DISCRIMINATOR_DEFAULT_NAME] = this.constructor.name;
  }
}
