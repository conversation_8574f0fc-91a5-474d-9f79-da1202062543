/**
 * 截断文本并确保总长度（包括省略号）不超过指定限制
 * @param text 需要截断的文本
 * @param limit 最大长度限制
 * @param ellipsis 省略号字符串，默认为'...'
 * @returns 截断后的文本
 */
export function truncate(text: string, limit: number, ellipsis = '...'): string {
  if (!text) return '';
  if (text.length <= limit) return text;

  // 确保截断后加上省略号的总长度不超过限制
  const maxLength = limit - ellipsis.length;
  if (maxLength <= 0) return ellipsis.slice(0, limit);

  return text.slice(0, maxLength) + ellipsis;
}

export function removeFileExtension(filename: string): string {
  // Handle edge cases
  if (!filename) return '';
  if (filename === '.' || filename === '..') return filename;

  // Find last dot position
  const lastDotIndex = filename.lastIndexOf('.');

  // Return original string if no extension found
  if (lastDotIndex === -1) return filename;

  // Handle special cases like .gitignore or .env
  if (lastDotIndex === 0) return filename;

  return filename.slice(0, lastDotIndex);
}

/**
 * 移除图片的 @large, @medium, @small 后缀，避免 SVG 图片展示问题
 */
export function removeImageSuffixes(text: string): string {
  // 匹配Markdown图片语法，正则捕获组分为前缀、链接和后缀
  const imageRegex = /(!?\[.*?\]\()(.*?)(\))/g;

  return text.replace(imageRegex, (match, prefix, url, suffix) => {
    // 移除@large, @medium, @small后缀
    const cleanUrl = url.replace(/@(large|medium|small)$/, '');
    return prefix + cleanUrl + suffix;
  });
}
