/**
 * Infrastructure Module - 基础设施模块
 * 提供所有基础设施服务的注册和导出
 *
 * Created for NestJS migration
 */

import { forwardRef, Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { DomainModule } from '@/domain/domain.module';
import { AppleService } from './apple';
import { BochaSearchService } from './bocha';
import { BraveSearchService } from './brave';
import { ClickhouseService } from './clickhouse';
import { GoogleService } from './google';
import { JinaService } from './jina';
import { LangfuseService } from './langfuse';
import { EmailService } from './plunk';
import { RedisService } from './redis';
import { S3Service } from './s3';
import { GoogleEventsService } from './serp/google-events.service';
import { GoogleScholarService } from './serp/google-scholar.service';
import { GoogleSearchService } from './serp/google-search.service';
import { YouTubeSearchService } from './serp/youtube.service';
import { SlackService } from './slack';
import { StripeService } from './stripe';
import { TypesenseService } from './typesense';
import youget, { Youget } from './youget';
import { BaseLLMService, YouappLangfuseLLM } from './youllm';

const infraServices = [
  AppleService,
  BochaSearchService,
  BraveSearchService,
  ClickhouseService,
  GoogleService,
  GoogleEventsService,
  GoogleScholarService,
  GoogleSearchService,
  YouTubeSearchService,
  JinaService,
  LangfuseService,
  EmailService,
  RedisService,
  S3Service,
  SlackService,
  StripeService,
  TypesenseService,
  BaseLLMService,
  YouappLangfuseLLM,
];

@Module({
  imports: [CqrsModule, forwardRef(() => DomainModule)],
  providers: [
    ...infraServices,
    {
      provide: Youget,
      useValue: youget,
    },
  ],
  exports: [...infraServices, Youget],
})
export class InfraModule {}
