/**
 * Google Scholar Search Service - Wrapper for SERP API Google Scholar search
 */

import { Injectable } from '@nestjs/common';
import {
  type GoogleScholarResult,
  type GoogleScholarSearchParams,
  searchGoogleScholar,
} from './google_scholar';

@Injectable()
export class GoogleScholarService {
  /**
   * Search Google Scholar using SERP API
   */
  async search(params: GoogleScholarSearchParams): Promise<GoogleScholarResult[]> {
    return searchGoogleScholar(params);
  }
}
