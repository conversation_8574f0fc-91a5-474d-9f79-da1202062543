/**
 * Google Search Service - Wrapper for SERP API Google search
 */

import { Injectable } from '@nestjs/common';
import { type GoogleSearchParams, type GoogleSearchResult, searchGoogle } from './google_search';

@Injectable()
export class GoogleSearchService {
  /**
   * Search Google using SERP API
   */
  async search(params: GoogleSearchParams): Promise<GoogleSearchResult[]> {
    return searchGoogle(params);
  }
}
