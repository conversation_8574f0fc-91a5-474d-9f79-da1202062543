/**
 * YouTube Search Service - Wrapper for SERP API YouTube search
 */

import { Injectable } from '@nestjs/common';
import { searchYouTube, type YouTubeSearchParams, type YouTubeVideoResult } from './youtube';

@Injectable()
export class YouTubeSearchService {
  /**
   * Search YouTube using SERP API
   */
  async search(params: YouTubeSearchParams): Promise<YouTubeVideoResult[]> {
    return searchYouTube(params);
  }
}
