/**
 * Google Events Search Service - Wrapper for SERP API Google Events search
 */

import { Injectable } from '@nestjs/common';
import {
  type GoogleEventResult,
  type GoogleEventsSearchParams,
  searchGoogleEvents,
} from './google_events';

@Injectable()
export class GoogleEventsService {
  /**
   * Search Google Events using SERP API
   */
  async search(params: GoogleEventsSearchParams): Promise<GoogleEventResult[]> {
    return searchGoogleEvents(params);
  }
}
