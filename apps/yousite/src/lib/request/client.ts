import { createCamelCaseApiClient, createSnakeCaseApiClient } from '@repo/api';
import { toast } from '@repo/ui/components/ui/sonner';
import { createCallHTTP, createCallHTTPStream } from '@repo/ui/lib/http';
import * as Sentry from '@sentry/nextjs';
import router from 'next/router';
import { UserWithPreferenceVO } from '@/schema/userSchema';

const isLocal = process.env.NODE_ENV === 'development';

export const apiClient = createCamelCaseApiClient({
  basePath: isLocal ? 'http://localhost:3003' : process.env.NEXT_PUBLIC_YOUMIND_API_BASE_PATH,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const apiClientOld = createSnakeCaseApiClient({
  basePath: isLocal ? 'http://localhost:3003' : '/',
  headers: {
    'Content-Type': 'application/json',
  },
});

export const callHTTP = createCallHTTP({
  errorReporter: {
    captureException: (error: any) => {
      Sentry.captureException(error);
    },
  },
  notificationService: {
    showError: (message, options) => {
      toast(message, options);
    },
  },
  navigationService: {
    redirectToLogin: () => {
      router.push('/login');
    },
    redirectTo404: () => {
      router.push('/404');
    },
  },
});

export const callHTTPStream = createCallHTTPStream({
  errorReporter: {
    captureException: (error) => {
      Sentry.captureException(error);
    },
  },
  notificationService: {
    showError: (message, options) => {
      toast(message, options);
    },
  },
  navigationService: {
    redirectToLogin: () => {
      router.push('/login');
    },
    redirectTo404: () => {
      router.push('/404');
    },
  },
  traceProvider: {
    getTraceParent: () => {
      return window.__TRACE_PARENT__;
    },
  },
});

export async function tryGetCurrentUserFromClient() {
  try {
    const user = await apiClient.userApi.getCurrentUser();
    return user as UserWithPreferenceVO;
  } catch (error: any) {
    return null;
  }
}
