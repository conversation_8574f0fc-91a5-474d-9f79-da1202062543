{"name": "@repo/config", "version": "1.0.0", "private": true, "license": "MIT", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["./dist/**"], "publishConfig": {"access": "public"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "pnpm run build:clean && tsc", "build:clean": "rm -rf dist", "type-check": "tsc --noEmit", "typecheck": "tsc --noEmit", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format ."}, "dependencies": {}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "typescript": "catalog:"}}