'use client';

import { AILanguageEnumKeys } from '@repo/common/types/language/enum';
import { LineIcon } from '@repo/ui/components/icons/line';
import { ParagraphIcon } from '@repo/ui/components/icons/paragraph';
import { TranslatorIcon } from '@repo/ui/components/icons/translator';
import { Button, ButtonWithTooltip } from '@repo/ui/components/ui/button';
import { toast } from '@repo/ui/components/ui/sonner';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { cn } from '@repo/ui/lib/utils';
import { isDev, isPreview } from '@youmindinc/youcommon';
import { useYouMindTranslate } from '@youmindinc/youmind-translate';
import { useAtom } from 'jotai';
import { Bot, Download, RefreshCcw } from 'lucide-react';
import { memo, useEffect, useState } from 'react';
import { snipDetailAtom } from '../../atoms';
import { useSnipContext } from '../../context';
import { useTranslation } from '../../i18n';
import { SnipArticle } from '../../typings/snip';
import { BubbleNavBar } from '../bubble-nav-bar';
import { TranslateLanguageSelector } from '../language-selector';
import {
  displayLineByLineAtom,
  formattedCuesAtom,
  hasTranscriptAtom,
  haveViewedParagraphAtom,
  isFormattingTranscriptAtom,
  isGeneratingTranscriptAtom,
  languageAtom,
  stickyTopAtom,
  translateLanguageAtom,
  unformattedCuesAtom,
} from './atoms';
import { useTranscriptExport } from './hooks';

export const TranscriptToolbar = memo(function TranscriptToolbar({
  handleReformatTranscript,
  renderRightActions,
}: {
  handleReformatTranscript: () => void;
  renderRightActions?: () => React.ReactNode;
}) {
  const { t } = useTranslation('Action.Transcript');

  const [displayLineByLine, setDisplayLineByLine] = useAtom(displayLineByLineAtom);
  const [hasTranscript] = useAtom(hasTranscriptAtom);
  const [language] = useAtom(languageAtom);
  const [translateLanguage, setTranslateLanguage] = useAtom(translateLanguageAtom);
  const { readonly } = useSnipContext();
  const [stickyTop] = useAtom(stickyTopAtom);
  const [formattedCues] = useAtom(formattedCuesAtom);
  const [unformattedCues] = useAtom(unformattedCuesAtom);
  const [haveViewedParagraph, setHaveViewedParagraph] = useAtom(haveViewedParagraphAtom);
  const { handleExport } = useTranscriptExport();
  const [isFormattingTranscript] = useAtom(isFormattingTranscriptAtom);
  const [isGeneratingTranscript] = useAtom(isGeneratingTranscriptAtom);

  const { trackButtonClick } = useTrackActions();

  const { handleTargetLanguageChange, handleTranslateTrigger, clearTranslation } =
    useYouMindTranslate();

  const [snip] = useAtom(snipDetailAtom);

  const [canExport, setCanExport] = useState(false);

  useEffect(() => {
    setTranslateLanguage(language as AILanguageEnumKeys);
  }, [language, setTranslateLanguage]);

  useEffect(() => {
    if (displayLineByLine) {
      setCanExport(unformattedCues.length > 0 && !isGeneratingTranscript);
    } else {
      setCanExport(formattedCues.length > 0 && !isFormattingTranscript);
    }
  }, [
    displayLineByLine,
    isGeneratingTranscript,
    isFormattingTranscript,
    formattedCues,
    unformattedCues,
  ]);

  const [canRegenerate, setCanRegenerate] = useState(false);
  useEffect(() => {
    setCanRegenerate(
      !isGeneratingTranscript && !isFormattingTranscript && unformattedCues.length > 0,
    );
  }, [isGeneratingTranscript, isFormattingTranscript, unformattedCues.length]);

  return (
    <div
      className="notranslate sticky top-0 z-40 -ml-2 mb-2 mt-2 flex w-[calc(100%+16px)] items-center justify-between bg-card px-2 pb-2 pt-3 transition-[top]"
      style={{
        top: stickyTop || 0,
      }}
    >
      <BubbleNavBar
        value={displayLineByLine ? 'line' : 'paragraph'}
        onChange={(value) => {
          const isLine = value === 'line';
          setDisplayLineByLine(isLine);
          if (!isLine) {
            setHaveViewedParagraph(true);
          }
          // 上报埋点
          trackButtonClick(
            isLine ? 'transcript_display_by_line_click' : 'transcript_display_by_paragraph_click',
            {
              snip_type: snip?.type,
              snip_id: snip?.id,
              webpage_url: (snip as SnipArticle)?.webpage?.url,
            },
          );
        }}
        items={[
          {
            value: 'line',
            label: t('displayLineByLine'),
            icon: <LineIcon size={16} />,
          },
          {
            value: 'paragraph',
            label: t('displayByParagraph'),
            icon: <ParagraphIcon size={16} />,
          },
        ]}
        className={cn((!haveViewedParagraph || isFormattingTranscript) && 'animate-pulse')}
      />
      <div className="flex items-center gap-x-2">
        {!readonly && (
          <>
            <TranslateLanguageSelector
              type="target"
              value={translateLanguage as AILanguageEnumKeys}
              hoverToOpen
              onChange={(value) => {
                // 如果选择的是原文语言，则清空翻译
                if (value === language) {
                  setTranslateLanguage(language as AILanguageEnumKeys);
                  clearTranslation();
                  return;
                }
                // 如果是反选
                if (translateLanguage === value) {
                  setTranslateLanguage(language as AILanguageEnumKeys);
                  clearTranslation();
                  return;
                }
                // 触发翻译
                handleTargetLanguageChange(value);
                setTranslateLanguage(value);
                setTimeout(() => {
                  handleTranslateTrigger();
                });
              }}
            >
              <Button size="sm" variant="icon" className="rounded-full">
                <TranslatorIcon size={16} />
              </Button>
            </TranslateLanguageSelector>

            <ButtonWithTooltip
              tooltip={t('export')}
              disabled={!canExport}
              size="sm"
              variant="icon"
              onClick={() => handleExport(displayLineByLine)}
            >
              <Download size={16} />
            </ButtonWithTooltip>

            <ButtonWithTooltip
              disabled={!canRegenerate}
              size="sm"
              variant="icon"
              tooltip={t('regenerate')}
              onClick={() => handleReformatTranscript()}
            >
              <RefreshCcw size={16} />
            </ButtonWithTooltip>

            {(isPreview() || isDev()) && isGeneratingTranscript && (
              <ButtonWithTooltip
                tooltip={
                  <div className="flex flex-col gap-1">
                    <span className="font-bold text-red-400">{t('previewWarning')}</span>
                    <span className="text-white">{t('previewWarningDescription')}</span>
                  </div>
                }
                size="sm"
                variant="icon"
                onClick={async () => {
                  toast('Fetching lastest transcript...');
                  try {
                    await fetch('https://youget-preview.vercel.app/api/cron', {
                      mode: 'no-cors',
                    });
                    toast('Transcript fetched, please wait for it to show up.');
                  } catch (error) {
                    toast('Failed to fetch transcript');
                  }
                }}
              >
                <Bot size={16} />
              </ButtonWithTooltip>
            )}
          </>
        )}

        {renderRightActions?.()}
      </div>
    </div>
  );
});
