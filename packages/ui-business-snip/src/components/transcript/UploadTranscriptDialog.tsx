'use client';

import {
  FileInput,
  FileUploader,
  FileUploaderContent,
} from '@repo/ui/components/custom/file-upload';
import { Button } from '@repo/ui/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/ui/dialog';
import { toast } from '@repo/ui/components/ui/sonner';
import { useEffect, useState } from 'react';
import { useTranslation } from '../../i18n';

interface UploadTranscriptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (transcript: string) => Promise<{ success: boolean; error?: string }>;
}

export function UploadTranscriptDialog({
  open,
  onOpenChange,
  onSubmit,
}: UploadTranscriptDialogProps) {
  const { t } = useTranslation('Action.Transcript');
  const [files, setFiles] = useState<File[] | null>(null);
  const [transcript, setTranscript] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (open) {
      checkClipboard();
    }
  }, [open]);

  const checkClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();

      if (isSrtContent(text)) {
        setTranscript(text);
        toast(t('clipboardSrtDetected'));
      }
    } catch (error) {
      console.error('Failed to read clipboard:', error);
    }
  };

  const isSrtContent = (content: string): boolean => {
    const srtEntries = content.trim().split('\n\n');
    if (srtEntries.length === 0) return false;

    const lines = srtEntries[0]?.split('\n');
    if (!lines || lines.length < 3) return false;

    // Check for timestamp line (format: 00:00:00,000 --> 00:00:01,460)
    const timestampLine = lines[1];
    const timestampMatch = timestampLine?.match(
      /(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/,
    );

    return !!timestampMatch;
  };

  const handleFilesChange = async (newFiles: File[] | null) => {
    setFiles(newFiles);

    if (!newFiles || newFiles.length === 0) {
      setTranscript('');
      return;
    }

    const file = newFiles[0];
    try {
      const text = (await file?.text()) || '';
      if (isSrtContent(text)) {
        setTranscript(text);
      } else {
        toast(t('invalidSrtFormat'));
        setFiles(null);
      }
    } catch (error) {
      console.error('Error reading file:', error);
      toast(t('fileReadError'));
      setFiles(null);
    }
  };

  const handleSubmit = async () => {
    if (!transcript) {
      toast(t('noTranscriptContent'));
      return;
    }

    setIsLoading(true);

    try {
      const { success: submitSuccess } = await onSubmit(transcript);
      if (submitSuccess) {
        handleClose();
        toast(t('transcriptUploaded'));
      } else {
        toast(t('transcriptUploadFailed'));
      }
    } catch (error) {
      console.error('Error submitting transcript:', error);
      toast(t('transcriptUploadFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setFiles(null);
    setTranscript('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('uploadTranscript')}</DialogTitle>
        </DialogHeader>

        <div className="my-4">
          <FileUploader
            value={files}
            onValueChange={handleFilesChange}
            dropzoneOptions={{
              accept: {
                'text/plain': ['.srt'],
              },
              maxFiles: 1,
              maxSize: 1024 * 1024 * 1, // 1MB
              multiple: false,
            }}
          >
            <FileInput className="flex min-h-[160px] flex-col items-center justify-center gap-2 rounded-xl border border-dashed border-muted-foreground/20 p-10">
              <div className="flex flex-col items-center justify-center gap-4 text-center">
                <div className="font-medium text-md text-muted-foreground">{t('dragDropSrt')}</div>
                <div className="text-sm text-muted-foreground">{t('uploadSrtDescription')}</div>
              </div>
            </FileInput>
            <FileUploaderContent />
          </FileUploader>
        </div>

        {transcript && (
          <div className="max-h-[200px] overflow-auto rounded-md border p-2 text-xs">
            <pre>
              {transcript.slice(0, 500)}
              {transcript.length > 500 ? '...' : ''}
            </pre>
          </div>
        )}

        <div className="flex justify-end mt-4 space-x-2">
          <Button variant="outline" onClick={handleClose}>
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={!transcript || isLoading}>
            {isLoading ? t('uploading') : t('upload')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
