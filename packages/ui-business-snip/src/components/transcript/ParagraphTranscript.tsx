'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button } from '@repo/ui/components/ui/button';
import { Skeleton } from '@repo/ui/components/ui/skeleton';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { MouseEvent, memo, useEffect, useRef, useState } from 'react';
import { useTranslation } from '../../i18n';

import { StarIcon } from '../icon';
import {
  formattedCuesAtom,
  isFormattingTranscriptAtom,
  paragraphRefAtom,
  unformattedCuesAtom,
} from './atoms';
import { EditableSpeakerLabel } from './EditableSpeakerLabel';
import { addCJKSpacing, speakerAvatars } from './utils';

export const ParagraphTranscript = memo(function ParagraphTranscript({
  seek,
  generateSubtitleWithPunctuation,
}: {
  seek: (timestamp: number | string) => void;
  generateSubtitleWithPunctuation: () => void;
}) {
  const { t } = useTranslation('Action.Transcript');

  const [unformattedCues] = useAtom(unformattedCuesAtom);
  const [formattedCues] = useAtom(formattedCuesAtom);
  const [isFormattingTranscript] = useAtom(isFormattingTranscriptAtom);
  const [, setParagraphRef] = useAtom(paragraphRefAtom);

  const [isDragging, setIsDragging] = useState(false);
  const [initialPosition, setInitialPosition] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: MouseEvent) => {
    setInitialPosition({ x: e.clientX, y: e.clientY });
    setIsDragging(false); // 初始化为非拖拽状态
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) {
      const distanceX = Math.abs(e.clientX - initialPosition.x);
      const distanceY = Math.abs(e.clientY - initialPosition.y);
      const threshold = 5; // 设置阈值距离
      if (distanceX > threshold || distanceY > threshold) {
        setIsDragging(true);
      }
    }
  };

  const handleMouseUp = (timestamp: string) => {
    if (!isDragging) {
      seek(timestamp);
    }
  };

  const [speakerCount, setSpeakerCount] = useState(0);
  useEffect(() => {
    const speakers = unformattedCues.reduce((acc, cue) => {
      if (cue.speaker) {
        acc.push(cue.speaker);
      }
      return acc;
    }, [] as string[]);
    const speakerCount = Array.from(new Set(speakers.filter((speaker) => speaker)));
    setSpeakerCount(Math.max(speakerCount.length, 1));
  }, [unformattedCues]);
  const paragraphRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setParagraphRef(paragraphRef);
  }, [setParagraphRef]);

  const renderFormattingLoading = (
    <div className="relative">
      <Skeleton className="w-full h-4 mb-4 bg-muted" />
      <Skeleton className="w-full h-4 mb-4 bg-muted" />
      <Skeleton className="w-full h-4 mb-4 bg-muted" />
      <Skeleton className="w-2/3 h-4 mb-4 bg-muted" />
      <StarIcon animate={true} size={20} className="absolute right-0 top-4" />
      <StarIcon animate={true} size={12} className="absolute left-0 bottom-6" />
    </div>
  );

  return (
    <>
      <div className="px-3 space-y-6 text-base leading-6 body text-foreground" ref={paragraphRef}>
        {formattedCues.length > 0 &&
          formattedCues.map(({ avatar, speaker, content }, i) => (
            <div key={i}>
              <div className="flex items-center mb-1 footnote notranslate gap-x-3">
                <Avatar className="object-cover w-6 h-6">
                  <AvatarImage src={avatar} alt="speaker avatar" className="w-6 h-6 rounded-full" />
                  <AvatarFallback></AvatarFallback>
                </Avatar>
                <div className="flex items-center">
                  {typeof speaker === 'string' &&
                  speaker.trim().length > 0 &&
                  speaker !== 'undefined' ? (
                    <EditableSpeakerLabel speaker={speaker} />
                  ) : (
                    <span className="text-caption">{t('speaker')}</span>
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-y-2 pl-9">
                {content.map(({ text, speaker, timestamp }, j) => {
                  return timestamp ? (
                    <div className="flex flex-col mt-1" key={`${speaker}-${timestamp}-${j}`}>
                      <i
                        className="font-mono text-2xs text-caption"
                        onClick={() => seek(timestamp)}
                        data-timestamp={timestamp}
                      >
                        {timestamp}
                      </i>
                      <span
                        data-timestamp={timestamp}
                        data-speaker={speaker}
                        contentEditable={false}
                        suppressContentEditableWarning={true}
                        className="px-2 py-1 -mx-2 -my-1 transition-colors rounded-md cursor-pointer ym-transcript-paragraph hover:bg-muted hover:text-foreground focus:underline focus:decoration-link focus:underline-offset-4"
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={() => handleMouseUp(timestamp)}
                      >
                        {addCJKSpacing(text)}
                      </span>
                    </div>
                  ) : (
                    <i key={j}>{addCJKSpacing(text)}</i>
                  );
                })}
              </div>
            </div>
          ))}
        {formattedCues.length === 0 && !isFormattingTranscript && (
          <div className="flex flex-col items-center py-8 gap-y-2">
            <div className="flex flex-row items-center gap-x-1">
              {speakerAvatars.slice(0, speakerCount).map((avatar, i) => (
                <Avatar key={i} className={`h-6 w-6 object-cover ${i > 0 ? '-ml-3' : ''}`}>
                  <AvatarImage src={avatar} alt="speaker avatar" />
                  <AvatarFallback></AvatarFallback>
                </Avatar>
              ))}
              <span className="text-sm text-caption">
                {t('multipleSpeakers', { count: speakerCount })}
              </span>
            </div>
            <div className="text-muted-foreground">{t('enhanceText')}</div>
            <Button
              variant="outline"
              className="body-strong group relative mt-2 h-8 w-[184px] overflow-hidden rounded-full border border-border bg-brand p-[1.5px] text-card hover:bg-brand/90 hover:text-card"
              disabled={isFormattingTranscript}
              onClick={() => {
                if (isFormattingTranscript) {
                  return;
                }
                generateSubtitleWithPunctuation();
              }}
            >
              <div className="absolute inset-0 h-full w-full animate-rotate rounded-full bg-[conic-gradient(#ffffff_20deg,transparent_120deg)] opacity-0 group-hover:opacity-100"></div>
              <div className="relative flex items-center justify-center w-full rounded-full h-7 bg-brand">
                <StarIcon size={16} className="mr-1" />
                {t('enhance')}
              </div>
            </Button>
          </div>
        )}
      </div>
      <AnimatePresence>
        {isFormattingTranscript && (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            {renderFormattingLoading}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
});
