import {
  ContentFormatEnum,
  LanguageEnum,
  ProcessStatusEnum,
} from '@repo/api/generated-client/snake-case/index';
import { iso6391ToLanguage } from '@repo/common/types/language/index';
import usePolling from '@repo/ui/hooks/usePolling';
import { getLanguageEnum } from '@repo/ui/lib/language';
import { useAtom } from 'jotai';
import { useCallback, useEffect } from 'react';
import { snipDetailAtom } from '../../../atoms';
import { useSnipContext } from '../../../context';
import { SnipVideo, SnipVoice, StreamDataTypeEnum } from '../../../typings/snip';
import {
  displayLineByLineAtom,
  formattedTranscriptContentsAtom,
  hasTranscriptAtom,
  isFormattingTranscriptAtom,
  isGeneratingTranscriptAtom,
  languageAtom,
  rawTranscriptContentsAtom,
  type TranscriptContent,
} from '../atoms';
import { selectLanguage } from '../utils';

export function useTranscriptState() {
  const [snipDetail] = useAtom(snipDetailAtom);
  const { user, apiClient, events, readonly, callHTTPStream } = useSnipContext();
  const [, setIsFormattingTranscript] = useAtom(isFormattingTranscriptAtom);
  const [rawTranscriptContents, setRawTranscriptContents] = useAtom(rawTranscriptContentsAtom);
  const [formattedTranscriptContents, setFormattedTranscriptContents] = useAtom(
    formattedTranscriptContentsAtom,
  );
  const [, setDisplayLineByLine] = useAtom(displayLineByLineAtom);
  const [language, setLanguage] = useAtom(languageAtom);
  const [, setHasTranscript] = useAtom(hasTranscriptAtom);
  const [isGeneratingTranscript, setIsGeneratingTranscript] = useAtom(isGeneratingTranscriptAtom);
  useEffect(() => {
    const hasTranscript = rawTranscriptContents.length > 0;
    setHasTranscript(hasTranscript);
    if (!hasTranscript) {
      setDisplayLineByLine(true);
    }
  }, [rawTranscriptContents, setDisplayLineByLine, setHasTranscript]);

  useEffect(() => {
    setIsGeneratingTranscript(
      rawTranscriptContents.some(({ status }) => status === ProcessStatusEnum.processing),
    );
  }, [rawTranscriptContents, setIsGeneratingTranscript]);

  useEffect(() => {
    const currentFormattedTranscriptContents = formattedTranscriptContents.filter(
      (content) => content.language === language,
    );
    if (currentFormattedTranscriptContents.length > 0) {
      setIsFormattingTranscript(
        currentFormattedTranscriptContents.some(
          ({ status }) => status === ProcessStatusEnum.processing,
        ),
      );
    }
  }, [formattedTranscriptContents, setIsFormattingTranscript, language]);

  useEffect(() => {
    if (rawTranscriptContents.length && !language) {
      setLanguage(selectLanguage(rawTranscriptContents, user));
    }
  }, [rawTranscriptContents, user, language, setLanguage]);

  const updateStateWhenSnipChanged = useCallback(
    (updatedSnip: SnipVideo | SnipVoice) => {
      const rawContents =
        updatedSnip.transcript?.contents?.filter(
          ({ format }) => format === ContentFormatEnum.subtitle,
        ) || [];
      const formattedContents =
        updatedSnip.transcript?.contents?.filter(
          ({ format }) => format === ContentFormatEnum.subtitleFormatted,
        ) || [];

      setRawTranscriptContents(rawContents as TranscriptContent[]);
      setFormattedTranscriptContents(formattedContents as TranscriptContent[]);
      setLanguage(selectLanguage(rawContents as TranscriptContent[], user));
    },
    [setRawTranscriptContents, setFormattedTranscriptContents, setLanguage, user],
  );

  const fetchData = useCallback(async () => {
    if (!snipDetail?.id) {
      return;
    }

    const data = (await apiClient.snipApi.getSnip({
      id: snipDetail.id,
    })) as SnipVideo | SnipVoice;

    if (data) {
      updateStateWhenSnipChanged(data);
    }
  }, [snipDetail?.id, updateStateWhenSnipChanged]);
  const { stopPolling, startPolling } = usePolling(fetchData, 5000);

  useEffect(() => {
    if (isGeneratingTranscript && !readonly) {
      startPolling();
    } else {
      stopPolling();
    }
    return () => {
      stopPolling();
    };
  }, [isGeneratingTranscript, readonly, startPolling, stopPolling]);

  const handleGenerateTranscript = useCallback(async () => {
    if (!snipDetail) return;

    const initialTranscript = {
      raw: '',
      plain: '',
      format: ContentFormatEnum.subtitle,
      status: ProcessStatusEnum.processing,
      id: '',
      language: language as LanguageEnum,
    } as TranscriptContent;

    setRawTranscriptContents([initialTranscript]);

    const { error } = await callHTTPStream('/api/v1/snip/generateTranscript', {
      method: 'POST',
      body: {
        snip_id: snipDetail.id,
        speaker_labels: true,
      },
      onMessage: (line) => {
        switch (line.type) {
          case StreamDataTypeEnum.data: {
            setRawTranscriptContents(line.data.contents);
            break;
          }
          case StreamDataTypeEnum.content: {
            setRawTranscriptContents((transcripts) => {
              if (!transcripts.length) {
                return [
                  {
                    raw: line.data,
                    plain: line.data,
                    format: ContentFormatEnum.subtitle,
                    status: ProcessStatusEnum.processing,
                    id: '',
                    language: language as LanguageEnum,
                  },
                ] as TranscriptContent[];
              }

              return [
                {
                  ...transcripts[0],
                  raw: (transcripts?.[0]?.raw || '') + line.data,
                  plain: (transcripts?.[0]?.plain || '') + line.data,
                },
              ] as TranscriptContent[];
            });
            break;
          }
          case StreamDataTypeEnum.error:
            break;
          default:
            break;
        }
      },
    });

    if (error) {
      setRawTranscriptContents([]);
      return;
    }

    await events?.onNeedRefreshSnip?.();
  }, [snipDetail, language, events, setRawTranscriptContents]);

  const handleUploadTranscript = useCallback(
    async (transcript: string) => {
      try {
        const languageCode = getLanguageEnum(transcript) || '';
        const locale = iso6391ToLanguage[languageCode] ?? 'en-US';

        try {
          await apiClient.snipApi.patchVideoTranscript({
            snip_id: snipDetail!.id,
            transcript: transcript,
            language: locale as LanguageEnum,
          });
          await events?.onNeedRefreshSnip?.();
          return { success: true };
        } catch (error: any) {
          return { success: false, error: error.message };
        }
      } catch (e) {
        console.error('Error uploading transcript:', e);
        return { success: false };
      }
    },
    [snipDetail, events],
  );

  return {
    updateStateWhenSnipChanged,
    handleGenerateTranscript,
    handleUploadTranscript,
  };
}
