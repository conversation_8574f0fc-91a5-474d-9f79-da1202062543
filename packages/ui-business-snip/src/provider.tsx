'use client';

import { SnipConfig } from './config';
import { SnipContext } from './context';
import { I18nProvider, Languages } from './i18n';

export function SnipProvider({
  config,
  children,
  locale,
}: {
  config: SnipConfig;
  children: React.ReactNode;
  locale?: Languages;
}) {
  return (
    <I18nProvider locale={locale}>
      <SnipContext.Provider value={config}>{children}</SnipContext.Provider>
    </I18nProvider>
  );
}
