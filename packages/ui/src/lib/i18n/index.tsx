import i18n, { Resource } from 'i18next';
import { ReactNode, useEffect } from 'react';
import { I18nextProvider, useTranslation as useI18nextTranslation } from 'react-i18next';

// ============= 有限深度的类型工具 =============

// 有限深度的嵌套键类型（最多5层，避免类型爆炸）
type NestedKeyOf<T, D extends number = 5> = D extends 0
  ? never
  : T extends object
    ? {
        [K in keyof T]: K extends string
          ? T[K] extends object
            ? `${K}` | `${K}.${NestedKeyOf<T[K], Prev<D>>}`
            : `${K}`
          : never;
      }[keyof T]
    : never;

// 递减计数器类型（限制递归深度）
type Prev<T extends number> = T extends 5
  ? 4
  : T extends 4
    ? 3
    : T extends 3
      ? 2
      : T extends 2
        ? 1
        : T extends 1
          ? 0
          : never;

// 根据路径获取值类型（限制深度）
type GetByPath<T, K extends string> = K extends keyof T
  ? T[K]
  : K extends `${infer Key}.${infer Rest}`
    ? Key extends keyof T
      ? GetByPath<T[Key], Rest>
      : never
    : never;

// 翻译函数类型（支持键类型推导，参数简化处理）
type TFunction<T extends object, NS extends string = never> = <
  K extends [NS] extends [never]
    ? NestedKeyOf<T>
    : GetByPath<T, NS> extends object
      ? NestedKeyOf<GetByPath<T, NS>>
      : string,
>(
  key: K,
  params?: Record<string, string | number>,
) => string;

// 配置类型
interface I18nConfig<Languages extends string = string, Resources = Record<string, unknown>> {
  translations: Record<Languages, Resources>;
  fallbackLanguage?: Languages;
  interpolation?: {
    escapeValue?: boolean;
    [key: string]: unknown;
  };
}

// ============= 实现 =============

// 创建 i18n 实例
function createI18nInstance<Languages extends string, Resources>(
  config: I18nConfig<Languages, Resources>,
) {
  const instance = i18n.createInstance();

  // 转换为 i18next 资源格式
  const resources: Resource = {};
  Object.entries(config.translations).forEach(([lang, translation]) => {
    resources[lang as string] = {
      translation: translation as Record<string, unknown>,
    };
  });

  instance.init({
    fallbackLng: config.fallbackLanguage,
    resources,
    interpolation: {
      escapeValue: false, // React 已经处理了 XSS 防护
      ...config.interpolation,
    },
    react: {
      useSuspense: false,
    },
  });

  return instance;
}

// ============= 导出的工厂函数 =============

export function createI18n<const Config extends I18nConfig<string, Record<string, unknown>>>(
  config: Config,
) {
  type Languages = keyof Config['translations'];
  type Resources = Config['translations'][Languages];

  const i18nInstance = createI18nInstance(config);

  // useTranslation hook 重载：支持带命名空间和不带命名空间
  function useTranslation<NS extends NestedKeyOf<Resources>>(
    namespace: NS,
  ): {
    t: TFunction<Resources, NS>;
    i18n: typeof i18nInstance;
    ready: boolean;
  };
  function useTranslation(): {
    t: TFunction<Resources>;
    i18n: typeof i18nInstance;
    ready: boolean;
  };
  function useTranslation<NS extends NestedKeyOf<Resources>>(namespace?: NS) {
    const {
      t: originalT,
      i18n,
      ready,
    } = useI18nextTranslation('translation', { i18n: i18nInstance });

    // 创建带类型的 t 函数
    const t = ((key: string, params?: Record<string, string | number>) => {
      const fullKey = namespace ? `${namespace}.${key}` : key;
      return originalT(fullKey, params);
    }) as TFunction<Resources, NS>;

    return {
      t,
      i18n,
      ready,
    };
  }

  return {
    I18nProvider: ({ children, locale = 'en-US' }: { children: ReactNode; locale?: Languages }) => {
      // 监听 locale 变化，动态切换语言
      useEffect(() => {
        if (locale && i18nInstance.language !== locale) {
          i18nInstance.changeLanguage(locale as string);
        }
      }, [locale]);

      return <I18nextProvider i18n={i18nInstance}>{children}</I18nextProvider>;
    },
    useTranslation,
    i18n: i18nInstance,
  };
}
