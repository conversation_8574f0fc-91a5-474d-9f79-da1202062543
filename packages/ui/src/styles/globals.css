:root {
  --background: 240 12% 95%;
  --foreground: 0, 0%, 0%, 0.88;

  --card: 0 0% 100%;
  --card-foreground: 0, 0%, 0%, 0.88;

  --popover: 0 0% 100%;
  --popover-foreground: 0, 0%, 0%, 0.88;

  --primary: 0, 0%, 0%, 0.88;
  --primary-foreground: 210 20% 98%;

  --secondary: 0, 0%, 65%;
  --secondary-foreground: 0 0% 0% / 0.6;

  --muted: 235, 86%, 5%, 0.07;
  --muted-foreground: 0, 0%, 0%, 0.6;
  --secondary-fg: var(--muted-foreground);

  --active: var(--muted);
  --active-foreground: 235, 86%, 5%, 0.16;

  --card-muted: 0, 0%, 100%, 0.12;

  --accent: 235, 86%, 5%, 0.04;
  --accent-foreground: 0, 0%, 0%, 0.88;

  --destructive: 3 100% 59%;
  --destructive-foreground: 0, 0%, 100%, 1;

  --inactive: 0, 0%, 100%, 0.64;

  --border: 240 1% 85%;
  --input: 235, 86%, 5%, 0.16;
  --ring: 240 10% 3.9%;
  --divider: 235, 86%, 5%, 0.12;

  --radius: 0.5rem;

  /** YouMind Custom CSS Variables **/
  --card-snips: 235, 86%, 5%, 0.04;
  --caption: 0, 0%, 0%, 0.4;
  --caption-fg: var(--caption);

  --disabled: 0, 0%, 0%, 0.24;
  --disabled-fg: var(--disabled);

  --primary-outline: 126 60% 58%;
  --primary-outline-foreground: 126 60% 58%;

  --block-background: 0, 0%, 100%;

  --brand: 0 0% 12%;
  --brand-hover: 0 0% 40%;
  --gray: var(--brand-hover);
  --brand-pressed: 0 0% 0%;
  --brand-disabled: 0 0% 0% 76%;
  --gray3: var(--brand-disabled);
  --brand-foreground: 0 0% 100%;
  --primary-fg: var(--brand-foreground);

  --error: 353, 87%, 67%, 1;

  --dark-blue-color: #3070f0;
  --blue-color: #61c5ff;
  --purple-color: #c39efd;
  --green-color: #9deb63;
  --red-color: #ff999c;
  --yellow-color: #ffdc52;
  --orange-color: #ff9500;

  --action-primary: var(--muted-foreground);
  --action-secondary: var(--caption);
  --action-primary-active: 0, 0%, 100%, 0.88;
  --action-secondary-active: 0, 0%, 100%, 0.6;
  --action-primary-active-note: 0, 0%, 14%, 0.88;
  --action-secondary-active-note: 0, 0%, 14%, 0.6;
  --link: 213, 100%, 43%;

  /* Board 的 Icon 颜色 */
  --function-gray: 240 2% 57%;
  --function-link: 211 100% 50%;
  --function-mint: 175 60% 60%;
  --function-green: 129 67% 48%;
  --function-indigo: 240 57% 58%;
  --function-purple: 274 63% 60%;
  --function-pink: 324 80% 55%;
  --function-red: 4 80% 59%;
  --function-orange: 35 100% 50%;
  --function-yellow: 48 100% 50%;
  --function-brown: 33 73% 29%;

  /* Assistant 的头像 */
  --assistant-avatar-icon-red: 4 80% 59%;
  --assistant-avatar-icon-brown: 40 72% 44%;
  --assistant-avatar-icon-yellow: 44 100% 49%;
  --assistant-avatar-icon-green: 137 53% 34%;
  --assistant-avatar-icon-blue: 217 100% 61%;
  --assistant-avatar-icon-purple: 274 63% 60%;
  --assistant-avatar-icon-pink: 321 100% 81%;
  --assistant-avatar-icon-gray: 43 6% 42%;

  --assistant-avatar-emoji-red: 0 95% 92%;
  --assistant-avatar-emoji-brown: 36 100% 88%;
  --assistant-avatar-emoji-yellow: 53 100% 84%;
  --assistant-avatar-emoji-green: 104 37% 92%;
  --assistant-avatar-emoji-blue: 223 100% 90%;
  --assistant-avatar-emoji-purple: 268 100% 93%;
  --assistant-avatar-emoji-pink: 318 87% 94%;
  --assistant-avatar-emoji-gray: 43 33% 93%;

  /* 黑白版本的 Assistant 的头像 */
  --assistant-icon: 210, 5%, 24%, 1;

  --tweet-container-margin: 1.5rem 0;
  --tweet-header-font-size: 0.9375rem;
  --tweet-header-line-height: 1.25rem;
  --tweet-body-font-size: 1.25rem;
  --tweet-body-font-weight: 400;
  --tweet-body-line-height: 1.5rem;
  --tweet-body-margin: 0;
  --tweet-quoted-container-margin: 0.75rem 0;
  --tweet-quoted-body-font-size: 0.938rem;
  --tweet-quoted-body-font-weight: 400;
  --tweet-quoted-body-line-height: 1.25rem;
  --tweet-quoted-body-margin: 0.25rem 0 0.75rem 0;
  --tweet-info-font-size: 0.9375rem;
  --tweet-info-line-height: 1.25rem;
  --tweet-actions-font-size: 0.875rem;
  --tweet-actions-line-height: 1rem;
  --tweet-actions-font-weight: 500;
  --tweet-actions-icon-size: 1.25em;
  --tweet-actions-icon-wrapper-size: calc(var(--tweet-actions-icon-size) + 0.75em);
  --tweet-replies-font-size: 0.875rem;
  --tweet-replies-line-height: 1rem;
  --tweet-replies-font-weight: 500;
  --tweet-skeleton-gradient: linear-gradient(270deg, #fafafa, #eaeaea, #eaeaea, #fafafa);
  --tweet-border: 1px solid #cfd9de;
  --tweet-font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --tweet-font-color: #0f1419;
  --tweet-font-color-secondary: #536471;
  --tweet-bg-color: #fff;
  --tweet-bg-color-hover: #f7f9f9;
  --tweet-quoted-bg-color-hover: rgba(0, 0, 0, 0.03);
  --tweet-color-blue-primary: #1d9bf0;
  --tweet-color-blue-primary-hover: #1a8cd8;
  --tweet-color-blue-secondary: #006fd6;
  --tweet-color-blue-secondary-hover: rgba(0, 111, 214, 0.1);
  --tweet-color-red-primary: #f91880;
  --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);
  --tweet-color-green-primary: #00ba7c;
  --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);
  --tweet-twitter-icon-color: var(--tweet-font-color);
  --tweet-verified-old-color: #829aab;
  --tweet-verified-blue-color: var(--tweet-color-blue-primary);
  --color-1: 0 100% 63%;
  --color-2: 270 100% 63%;
  --color-3: 210 100% 63%;
  --color-4: 195 100% 63%;
  --color-5: 90 100% 63%;

  --rich-panel-background: 0, 0%, 100%, 1;

  .dark {
    --background: 210 6% 13%;
    --foreground: 0, 0%, 100%, 0.88;

    --global-background: 236 12% 12% 1;

    --card: 0 0% 10%;
    --card-foreground: 0, 0%, 100%, 0.88;

    --popover: 0 0% 18%;
    --popover-foreground: 0, 0%, 100%, 0.88;

    --primary: 0, 0%, 100%, 0.88;
    --primary-foreground: 0, 0%, 0%, 0.88;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 100% 0.6;

    --muted: 0, 0%, 100%, 0.1;
    --muted-foreground: 0, 0%, 100%, 0.6;
    --secondary-fg: var(--muted-foreground);

    --accent: 0, 0%, 100%, 0.06;
    --accent-foreground: 0, 0%, 100%, 0.88;

    --destructive: 3 100% 61%;
    --destructive-foreground: 0, 0%, 100%, 1;

    --inactive: 0, 0%, 10%, 0.64;

    --border: 240 1% 85%;
    --input: 0, 0%, 100%, 0.2;
    --ring: 240 4.9% 83.9%;
    --divider: 0, 0%, 100%, 0.16;

    /** YouMind Custom CSS Variables **/
    --card-snips: 0, 0%, 100%, 0.06;
    --caption: 0, 0%, 100%, 0.4;
    --caption-fg: var(--caption);
    --disabled: 0, 0%, 100%, 0.3;
    --disabled-fg: var(--disabled);

    --active: var(--muted);
    --active-foreground: 0, 0%, 100%, 0.88;

    --primary-outline: 128 49% 59%;
    --primary-outline-foreground: 128 49% 59%;

    --block-background: 0, 0%, 14%;

    --brand: 0 0% 100%;
    --brand-hover: 0 0% 90%;
    --gray: var(--brand-hover);
    --brand-pressed: 240 1% 65%;
    --brand-disabled: 180 1% 39%;
    --gray3: var(--brand-disabled);
    --brand-foreground: 0 0% 12%;
    --primary-fg: var(--brand-foreground);

    --error: 353, 87%, 67%, 1;

    --action-primary: var(--foreground);
    --action-secondary: var(--caption);

    /* Board 的 Icon 颜色 */
    --function-gray: 240 2% 57%;
    --function-link: 211 100% 50%;
    --function-mint: 175 60% 60%;
    --function-green: 129 67% 48%;
    --function-indigo: 240 57% 58%;
    --function-purple: 274 63% 60%;
    --function-pink: 324 80% 55%;
    --function-red: 4 80% 59%;
    --function-orange: 35 100% 50%;
    --function-yellow: 48 100% 50%;
    --function-brown: 33 73% 29%;

    /* 黑白版本的 Assistant 的头像 */
    --assistant-icon: 0, 0%, 100%, 1;

    --tweet-skeleton-gradient: linear-gradient(270deg, #15202b, #1e2732, #1e2732, #15202b);
    --tweet-border: 1px solid #425364;
    --tweet-font-family:
      -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --tweet-font-color: #f7f9f9;
    --tweet-font-color-secondary: #8b98a5;
    --tweet-bg-color: #15202b;
    --tweet-bg-color-hover: #1e2732;
    --tweet-quoted-bg-color-hover: hsla(0, 0%, 100%, 0.03);
    --tweet-color-blue-primary: #1d9bf0;
    --tweet-color-blue-primary-hover: #1a8cd8;
    --tweet-color-blue-secondary: #6bc9fb;
    --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);
    --tweet-color-red-primary: #f91880;
    --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);
    --tweet-color-green-primary: #00ba7c;
    --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);
    --tweet-twitter-icon-color: var(--tweet-font-color);
    --tweet-verified-old-color: #829aab;
    --tweet-verified-blue-color: #fff;
    --color-1: 0 100% 63%;
    --color-2: 270 100% 63%;
    --color-3: 210 100% 63%;
    --color-4: 195 100% 63%;
    --color-5: 90 100% 63%;

    --rich-panel-background: 0, 0%, 0%, 0.88;
  }
}

/* 使用 CSS 变量 */
:root {
  --scrollbar-thumb: rgba(0, 0, 0, 0.1);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.2);
  --color-1: 0 100% 63%;
  --color-2: 270 100% 63%;
  --color-3: 210 100% 63%;
  --color-4: 195 100% 63%;
  --color-5: 90 100% 63%;
}

::-webkit-scrollbar {
  width: 4px; /* 修改滚动条宽度,默认是16px */
  height: 4px; /* 设置横向滚动条的高度 */
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}
/* Firefox */
* {
  scrollbar-color: var(--scrollbar-thumb) transparent;
}

* {
  @apply border-border;
}
body {
  @apply bg-background text-foreground;
}

/*---break---*/

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}
@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}

/*---break---*/

.dark {
  --color-1: 0 100% 63%;
  --color-2: 270 100% 63%;
  --color-3: 210 100% 63%;
  --color-4: 195 100% 63%;
  --color-5: 90 100% 63%;
}

/*---break---*/

* {
  @apply border-border outline-ring/50;
}
body {
  @apply bg-background text-foreground;
}
