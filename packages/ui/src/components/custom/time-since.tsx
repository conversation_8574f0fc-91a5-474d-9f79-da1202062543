import { useEffect, useRef, useState } from 'react';

type CalcTimeSinceParams = {
  dateString: Date | string;
  format?: 'github' | 'google_news';
  options?: { ignoreSeconds?: boolean };
};

export function calcTimeSince({
  dateString,
  format = 'google_news',
  options,
}: CalcTimeSinceParams): string {
  if (!dateString) {
    return '';
  }

  const now = new Date();
  const past = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

  if (diffInSeconds < 0) {
    return 'seconds ago';
  }

  if (options?.ignoreSeconds !== false) {
    if (diffInSeconds < 60) {
      return 'seconds ago';
    }
  }

  /** GitHub */
  if (format === 'github') {
    const intervals = [
      { label: 'year', seconds: 31536000 },
      { label: 'month', seconds: 2592000 },
      { label: 'week', seconds: 604800 },
      { label: 'day', seconds: 86400 },
      { label: 'hour', seconds: 3600 },
      { label: 'minute', seconds: 60 },
      { label: 'second', seconds: 1 },
    ];

    for (const interval of intervals) {
      const count = Math.floor(diffInSeconds / interval.seconds);
      if (count >= 1) {
        return count === 1 ? `1 ${interval.label}` : `${count} ${interval.label}s`;
      }
    }
    return 'seconds ago';
  }

  /** Google News */
  const secondsInMinute = 60;
  const secondsInHour = 60 * secondsInMinute;
  const secondsInDay = 24 * secondsInHour;

  if (diffInSeconds < secondsInMinute) {
    return diffInSeconds === 1 ? '1 second ago' : `${diffInSeconds} seconds ago`;
  } else if (diffInSeconds < secondsInHour) {
    const minutes = Math.floor(diffInSeconds / secondsInMinute);
    return minutes === 1 ? '1 minute ago' : `${minutes} minutes ago`;
  } else if (diffInSeconds < secondsInDay) {
    const hours = Math.floor(diffInSeconds / secondsInHour);
    return hours === 1 ? '1 hour ago' : `${hours} hours ago`;
  } else {
    const days = Math.floor(diffInSeconds / secondsInDay);
    if (days === 1) {
      return 'yesterday';
    } else if (days < 7) {
      return days === 1 ? '1 day ago' : `${days} days ago`;
    } else {
      return past.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    }
  }
}

export const TimeSince: React.FC<CalcTimeSinceParams> = ({
  dateString,
  format = 'google_news',
  options,
}) => {
  const [_, setFlag] = useState({});
  const interval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 每隔 30 秒刷新一次，确保时间显示正确
    interval.current = setInterval(() => {
      setFlag({});
    }, 30000);
    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, []);

  return <>{calcTimeSince({ dateString, format, options })}</>;
};
