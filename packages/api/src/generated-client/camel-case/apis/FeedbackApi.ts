/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type { SubmitFeedbackDto, SubmitFeedbackResponseDto } from '../models/index';
import {
  SubmitFeedbackDtoFromJSON,
  SubmitFeedbackDtoToJSON,
  SubmitFeedbackResponseDtoFromJSON,
  SubmitFeedbackResponseDtoToJSON,
} from '../models/index';

export interface FeedbackControllerSubmitFeedbackRequest {
  submitFeedbackDto: SubmitFeedbackDto;
}

/**
 * FeedbackApi - interface
 *
 * @export
 * @interface FeedbackApiInterface
 */
export interface FeedbackApiInterface {
  /**
   * 提交用户反馈到Linear系统
   * @summary 提交反馈
   * @param {SubmitFeedbackDto} submitFeedbackDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FeedbackApiInterface
   */
  submitFeedbackRaw(
    requestParameters: FeedbackControllerSubmitFeedbackRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<SubmitFeedbackResponseDto>>;

  /**
   * 提交用户反馈到Linear系统
   * 提交反馈
   */
  submitFeedback(
    submitFeedbackDto: SubmitFeedbackDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<SubmitFeedbackResponseDto>;
}

/**
 *
 */
export class FeedbackApi extends runtime.BaseAPI implements FeedbackApiInterface {
  /**
   * 提交用户反馈到Linear系统
   * 提交反馈
   */
  async submitFeedbackRaw(
    requestParameters: FeedbackControllerSubmitFeedbackRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<SubmitFeedbackResponseDto>> {
    if (requestParameters.submitFeedbackDto == null) {
      throw new runtime.RequiredError(
        'submitFeedbackDto',
        'Required parameter "submitFeedbackDto" was null or undefined when calling submitFeedback().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/submitFeedback`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: SubmitFeedbackDtoToJSON(requestParameters.submitFeedbackDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      SubmitFeedbackResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 提交用户反馈到Linear系统
   * 提交反馈
   */
  async submitFeedback(
    submitFeedbackDto: SubmitFeedbackDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<SubmitFeedbackResponseDto> {
    const response = await this.submitFeedbackRaw(
      { submitFeedbackDto: submitFeedbackDto },
      initOverrides,
    );
    return await response.value();
  }
}
