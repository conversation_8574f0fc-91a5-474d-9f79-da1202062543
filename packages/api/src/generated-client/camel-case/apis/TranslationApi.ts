/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type { TranslateDto, TranslationResultDto } from '../models/index';
import {
  TranslateDtoFromJSON,
  TranslateDtoToJSON,
  TranslationResultDtoFromJSON,
  TranslationResultDtoToJSON,
} from '../models/index';

export interface TranslationControllerTranslateRequest {
  translateDto: TranslateDto;
}

/**
 * TranslationApi - interface
 *
 * @export
 * @interface TranslationApiInterface
 */
export interface TranslationApiInterface {
  /**
   * 翻译单个或多个文本到目标语言
   * @summary 翻译文本
   * @param {TranslateDto} translateDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranslationApiInterface
   */
  translateRaw(
    requestParameters: TranslationControllerTranslateRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<TranslationResultDto>>>;

  /**
   * 翻译单个或多个文本到目标语言
   * 翻译文本
   */
  translate(
    translateDto: TranslateDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<TranslationResultDto>>;
}

/**
 *
 */
export class TranslationApi extends runtime.BaseAPI implements TranslationApiInterface {
  /**
   * 翻译单个或多个文本到目标语言
   * 翻译文本
   */
  async translateRaw(
    requestParameters: TranslationControllerTranslateRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<TranslationResultDto>>> {
    if (requestParameters.translateDto == null) {
      throw new runtime.RequiredError(
        'translateDto',
        'Required parameter "translateDto" was null or undefined when calling translate().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/translate`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: TranslateDtoToJSON(requestParameters.translateDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(TranslationResultDtoFromJSON),
    );
  }

  /**
   * 翻译单个或多个文本到目标语言
   * 翻译文本
   */
  async translate(
    translateDto: TranslateDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<TranslationResultDto>> {
    const response = await this.translateRaw({ translateDto: translateDto }, initOverrides);
    return await response.value();
  }
}
