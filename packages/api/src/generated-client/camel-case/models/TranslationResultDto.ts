/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface TranslationResultDto
 */
export interface TranslationResultDto {
  /**
   * 翻译后的文本
   * @type {string}
   * @memberof TranslationResultDto
   */
  translatedText: string;
  /**
   * 检测到的源语言
   * @type {string}
   * @memberof TranslationResultDto
   */
  sourceLanguage: string;
  /**
   * 目标语言
   * @type {string}
   * @memberof TranslationResultDto
   */
  targetLanguage: string;
  /**
   * 使用的翻译服务
   * @type {string}
   * @memberof TranslationResultDto
   */
  serviceName: string;
}

/**
 * Check if a given object implements the TranslationResultDto interface.
 */
export function instanceOfTranslationResultDto(value: object): value is TranslationResultDto {
  if (!('translatedText' in value) || value.translatedText === undefined) return false;
  if (!('sourceLanguage' in value) || value.sourceLanguage === undefined) return false;
  if (!('targetLanguage' in value) || value.targetLanguage === undefined) return false;
  if (!('serviceName' in value) || value.serviceName === undefined) return false;
  return true;
}

export function TranslationResultDtoFromJSON(json: any): TranslationResultDto {
  return TranslationResultDtoFromJSONTyped(json, false);
}

export function TranslationResultDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): TranslationResultDto {
  if (json == null) {
    return json;
  }
  return {
    translatedText: json.translatedText,
    sourceLanguage: json.sourceLanguage,
    targetLanguage: json.targetLanguage,
    serviceName: json.serviceName,
  };
}

export function TranslationResultDtoToJSON(json: any): TranslationResultDto {
  return TranslationResultDtoToJSONTyped(json, false);
}

export function TranslationResultDtoToJSONTyped(
  value?: TranslationResultDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    translatedText: value.translatedText,
    sourceLanguage: value.sourceLanguage,
    targetLanguage: value.targetLanguage,
    serviceName: value.serviceName,
  };
}
