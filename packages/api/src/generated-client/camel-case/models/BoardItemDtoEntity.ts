/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { ArticleDto } from './ArticleDto';
import {
  ArticleDtoFromJSON,
  ArticleDtoFromJSONTyped,
  ArticleDtoToJSON,
  instanceOfArticleDto,
} from './ArticleDto';
import type { BoardGroupDto } from './BoardGroupDto';
import {
  BoardGroupDtoFromJSON,
  BoardGroupDtoFromJSONTyped,
  BoardGroupDtoToJSON,
  instanceOfBoardGroupDto,
} from './BoardGroupDto';
import type { ImageDto } from './ImageDto';
import {
  ImageDtoFromJSON,
  ImageDtoFromJSONTyped,
  ImageDtoToJSON,
  instanceOfImageDto,
} from './ImageDto';
import type { OfficeDto } from './OfficeDto';
import {
  instanceOfOfficeDto,
  OfficeDtoFromJSON,
  OfficeDtoFromJSONTyped,
  OfficeDtoToJSON,
} from './OfficeDto';
import type { OtherWebpageDto } from './OtherWebpageDto';
import {
  instanceOfOtherWebpageDto,
  OtherWebpageDtoFromJSON,
  OtherWebpageDtoFromJSONTyped,
  OtherWebpageDtoToJSON,
} from './OtherWebpageDto';
import type { PdfDto } from './PdfDto';
import { instanceOfPdfDto, PdfDtoFromJSON, PdfDtoFromJSONTyped, PdfDtoToJSON } from './PdfDto';
import type { SnippetDto } from './SnippetDto';
import {
  instanceOfSnippetDto,
  SnippetDtoFromJSON,
  SnippetDtoFromJSONTyped,
  SnippetDtoToJSON,
} from './SnippetDto';
import type { TextDto } from './TextDto';
import { instanceOfTextDto, TextDtoFromJSON, TextDtoFromJSONTyped, TextDtoToJSON } from './TextDto';
import type { ThoughtDto } from './ThoughtDto';
import {
  instanceOfThoughtDto,
  ThoughtDtoFromJSON,
  ThoughtDtoFromJSONTyped,
  ThoughtDtoToJSON,
} from './ThoughtDto';
import type { UnknownWebpageDto } from './UnknownWebpageDto';
import {
  instanceOfUnknownWebpageDto,
  UnknownWebpageDtoFromJSON,
  UnknownWebpageDtoFromJSONTyped,
  UnknownWebpageDtoToJSON,
} from './UnknownWebpageDto';
import type { VideoDto } from './VideoDto';
import {
  instanceOfVideoDto,
  VideoDtoFromJSON,
  VideoDtoFromJSONTyped,
  VideoDtoToJSON,
} from './VideoDto';
import type { VoiceDto } from './VoiceDto';
import {
  instanceOfVoiceDto,
  VoiceDtoFromJSON,
  VoiceDtoFromJSONTyped,
  VoiceDtoToJSON,
} from './VoiceDto';
/**
 * @type BoardItemDtoEntity
 * Entity Data
 * @export
 */
export type BoardItemDtoEntity =
  | ArticleDto
  | BoardGroupDto
  | ImageDto
  | OfficeDto
  | OtherWebpageDto
  | PdfDto
  | SnippetDto
  | TextDto
  | ThoughtDto
  | UnknownWebpageDto
  | VideoDto
  | VoiceDto;

export function BoardItemDtoEntityFromJSON(json: any): BoardItemDtoEntity {
  return BoardItemDtoEntityFromJSONTyped(json, false);
}

export function BoardItemDtoEntityFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): BoardItemDtoEntity {
  if (json == null) {
    return json;
  }

  switch (json.$class) {
    case 'ArticleDto':
      return ArticleDtoFromJSONTyped(json, true);
    case 'BoardGroupDto':
      return BoardGroupDtoFromJSONTyped(json, true);
    case 'ImageDto':
      return ImageDtoFromJSONTyped(json, true);
    case 'OfficeDto':
      return OfficeDtoFromJSONTyped(json, true);
    case 'OtherWebpageDto':
      return OtherWebpageDtoFromJSONTyped(json, true);
    case 'PdfDto':
      return PdfDtoFromJSONTyped(json, true);
    case 'SnippetDto':
      return SnippetDtoFromJSONTyped(json, true);
    case 'TextDto':
      return TextDtoFromJSONTyped(json, true);
    case 'ThoughtDto':
      return ThoughtDtoFromJSONTyped(json, true);
    case 'UnknownWebpageDto':
      return UnknownWebpageDtoFromJSONTyped(json, true);
    case 'VideoDto':
      return VideoDtoFromJSONTyped(json, true);
    case 'VoiceDto':
      return VoiceDtoFromJSONTyped(json, true);
    default:
      return json;
  }
}

export function BoardItemDtoEntityToJSON(json: any): any {
  return BoardItemDtoEntityToJSONTyped(json, false);
}

export function BoardItemDtoEntityToJSONTyped(
  value?: any,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  // First try to use discriminator property if it exists
  if (value.$class) {
    switch (value.$class) {
      case 'ArticleDto':
        return ArticleDtoToJSON(value);
      case 'BoardGroupDto':
        return BoardGroupDtoToJSON(value);
      case 'ImageDto':
        return ImageDtoToJSON(value);
      case 'OfficeDto':
        return OfficeDtoToJSON(value);
      case 'OtherWebpageDto':
        return OtherWebpageDtoToJSON(value);
      case 'PdfDto':
        return PdfDtoToJSON(value);
      case 'SnippetDto':
        return SnippetDtoToJSON(value);
      case 'TextDto':
        return TextDtoToJSON(value);
      case 'ThoughtDto':
        return ThoughtDtoToJSON(value);
      case 'UnknownWebpageDto':
        return UnknownWebpageDtoToJSON(value);
      case 'VideoDto':
        return VideoDtoToJSON(value);
      case 'VoiceDto':
        return VoiceDtoToJSON(value);
      default:
        // Fall through to instanceOf checks
        break;
    }
  }

  // Fallback to instanceOf checks
  if (instanceOfArticleDto(value)) {
    return ArticleDtoToJSON(value);
  }
  if (instanceOfBoardGroupDto(value)) {
    return BoardGroupDtoToJSON(value);
  }
  if (instanceOfImageDto(value)) {
    return ImageDtoToJSON(value);
  }
  if (instanceOfOfficeDto(value)) {
    return OfficeDtoToJSON(value);
  }
  if (instanceOfOtherWebpageDto(value)) {
    return OtherWebpageDtoToJSON(value);
  }
  if (instanceOfPdfDto(value)) {
    return PdfDtoToJSON(value);
  }
  if (instanceOfSnippetDto(value)) {
    return SnippetDtoToJSON(value);
  }
  if (instanceOfTextDto(value)) {
    return TextDtoToJSON(value);
  }
  if (instanceOfThoughtDto(value)) {
    return ThoughtDtoToJSON(value);
  }
  if (instanceOfUnknownWebpageDto(value)) {
    return UnknownWebpageDtoToJSON(value);
  }
  if (instanceOfVideoDto(value)) {
    return VideoDtoToJSON(value);
  }
  if (instanceOfVoiceDto(value)) {
    return VoiceDtoToJSON(value);
  }

  return {};
}
