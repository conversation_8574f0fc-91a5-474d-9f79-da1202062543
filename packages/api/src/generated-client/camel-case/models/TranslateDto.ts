/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface TranslateDto
 */
export interface TranslateDto {
  /**
   * 要翻译的文本数组
   * @type {Array<string>}
   * @memberof TranslateDto
   */
  texts: Array<string>;
  /**
   * 目标语言代码
   * @type {string}
   * @memberof TranslateDto
   */
  targetLang: string;
  /**
   * 源语言代码（可选，自动检测）
   * @type {string}
   * @memberof TranslateDto
   */
  sourceLang?: string;
  /**
   * 指定翻译服务（可选）
   * @type {string}
   * @memberof TranslateDto
   */
  serviceName?: string;
}

/**
 * Check if a given object implements the TranslateDto interface.
 */
export function instanceOfTranslateDto(value: object): value is TranslateDto {
  if (!('texts' in value) || value.texts === undefined) return false;
  if (!('targetLang' in value) || value.targetLang === undefined) return false;
  return true;
}

export function TranslateDtoFromJSON(json: any): TranslateDto {
  return TranslateDtoFromJSONTyped(json, false);
}

export function TranslateDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): TranslateDto {
  if (json == null) {
    return json;
  }
  return {
    texts: json.texts,
    targetLang: json.targetLang,
    sourceLang: json.sourceLang == null ? undefined : json.sourceLang,
    serviceName: json.serviceName == null ? undefined : json.serviceName,
  };
}

export function TranslateDtoToJSON(json: any): TranslateDto {
  return TranslateDtoToJSONTyped(json, false);
}

export function TranslateDtoToJSONTyped(
  value?: TranslateDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    texts: value.texts,
    targetLang: value.targetLang,
    sourceLang: value.sourceLang,
    serviceName: value.serviceName,
  };
}
