/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface SubmitFeedbackDto
 */
export interface SubmitFeedbackDto {
  /**
   * 反馈描述
   * @type {string}
   * @memberof SubmitFeedbackDto
   */
  description: string;
  /**
   * 当前页面URL
   * @type {string}
   * @memberof SubmitFeedbackDto
   */
  currentUrl: string;
  /**
   * 图片URL列表
   * @type {Array<string>}
   * @memberof SubmitFeedbackDto
   */
  imageUrlList?: Array<string>;
  /**
   * 插件版本
   * @type {string}
   * @memberof SubmitFeedbackDto
   */
  extensionVersion?: string;
  /**
   * 插件是否已安装
   * @type {boolean}
   * @memberof SubmitFeedbackDto
   */
  extensionInstalled?: boolean;
}

/**
 * Check if a given object implements the SubmitFeedbackDto interface.
 */
export function instanceOfSubmitFeedbackDto(value: object): value is SubmitFeedbackDto {
  if (!('description' in value) || value.description === undefined) return false;
  if (!('currentUrl' in value) || value.currentUrl === undefined) return false;
  return true;
}

export function SubmitFeedbackDtoFromJSON(json: any): SubmitFeedbackDto {
  return SubmitFeedbackDtoFromJSONTyped(json, false);
}

export function SubmitFeedbackDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SubmitFeedbackDto {
  if (json == null) {
    return json;
  }
  return {
    description: json.description,
    currentUrl: json.currentUrl,
    imageUrlList: json.imageUrlList == null ? undefined : json.imageUrlList,
    extensionVersion: json.extensionVersion == null ? undefined : json.extensionVersion,
    extensionInstalled: json.extensionInstalled == null ? undefined : json.extensionInstalled,
  };
}

export function SubmitFeedbackDtoToJSON(json: any): SubmitFeedbackDto {
  return SubmitFeedbackDtoToJSONTyped(json, false);
}

export function SubmitFeedbackDtoToJSONTyped(
  value?: SubmitFeedbackDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    description: value.description,
    currentUrl: value.currentUrl,
    imageUrlList: value.imageUrlList,
    extensionVersion: value.extensionVersion,
    extensionInstalled: value.extensionInstalled,
  };
}
