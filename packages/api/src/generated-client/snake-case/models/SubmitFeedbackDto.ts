/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface SubmitFeedbackDto
 */
export interface SubmitFeedbackDto {
  /**
   * 反馈描述
   * @type {string}
   * @memberof SubmitFeedbackDto
   */
  description: string;
  /**
   * 当前页面URL
   * @type {string}
   * @memberof SubmitFeedbackDto
   */
  current_url: string;
  /**
   * 图片URL列表
   * @type {Array<string>}
   * @memberof SubmitFeedbackDto
   */
  image_url_list?: Array<string>;
  /**
   * 插件版本
   * @type {string}
   * @memberof SubmitFeedbackDto
   */
  extension_version?: string;
  /**
   * 插件是否已安装
   * @type {boolean}
   * @memberof SubmitFeedbackDto
   */
  extension_installed?: boolean;
}

/**
 * Check if a given object implements the SubmitFeedbackDto interface.
 */
export function instanceOfSubmitFeedbackDto(value: object): value is SubmitFeedbackDto {
  if (!('description' in value) || value.description === undefined) return false;
  if (!('current_url' in value) || value.current_url === undefined) return false;
  return true;
}

export function SubmitFeedbackDtoFromJSON(json: any): SubmitFeedbackDto {
  return SubmitFeedbackDtoFromJSONTyped(json, false);
}

export function SubmitFeedbackDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SubmitFeedbackDto {
  if (json == null) {
    return json;
  }
  return {
    description: json.description,
    current_url: json.current_url,
    image_url_list: json.image_url_list == null ? undefined : json.image_url_list,
    extension_version: json.extension_version == null ? undefined : json.extension_version,
    extension_installed: json.extension_installed == null ? undefined : json.extension_installed,
  };
}

export function SubmitFeedbackDtoToJSON(json: any): SubmitFeedbackDto {
  return SubmitFeedbackDtoToJSONTyped(json, false);
}

export function SubmitFeedbackDtoToJSONTyped(
  value?: SubmitFeedbackDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    description: value.description,
    current_url: value.current_url,
    image_url_list: value.image_url_list,
    extension_version: value.extension_version,
    extension_installed: value.extension_installed,
  };
}
