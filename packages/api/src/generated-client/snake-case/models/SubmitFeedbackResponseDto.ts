/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface SubmitFeedbackResponseDto
 */
export interface SubmitFeedbackResponseDto {
  /**
   * 是否成功
   * @type {boolean}
   * @memberof SubmitFeedbackResponseDto
   */
  success: boolean;
}

/**
 * Check if a given object implements the SubmitFeedbackResponseDto interface.
 */
export function instanceOfSubmitFeedbackResponseDto(
  value: object,
): value is SubmitFeedbackResponseDto {
  if (!('success' in value) || value.success === undefined) return false;
  return true;
}

export function SubmitFeedbackResponseDtoFromJSON(json: any): SubmitFeedbackResponseDto {
  return SubmitFeedbackResponseDtoFromJSONTyped(json, false);
}

export function SubmitFeedbackResponseDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SubmitFeedbackResponseDto {
  if (json == null) {
    return json;
  }
  return {
    success: json.success,
  };
}

export function SubmitFeedbackResponseDtoToJSON(json: any): SubmitFeedbackResponseDto {
  return SubmitFeedbackResponseDtoToJSONTyped(json, false);
}

export function SubmitFeedbackResponseDtoToJSONTyped(
  value?: SubmitFeedbackResponseDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    success: value.success,
  };
}
