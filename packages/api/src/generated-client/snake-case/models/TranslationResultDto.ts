/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface TranslationResultDto
 */
export interface TranslationResultDto {
  /**
   * 翻译后的文本
   * @type {string}
   * @memberof TranslationResultDto
   */
  translated_text: string;
  /**
   * 检测到的源语言
   * @type {string}
   * @memberof TranslationResultDto
   */
  source_language: string;
  /**
   * 目标语言
   * @type {string}
   * @memberof TranslationResultDto
   */
  target_language: string;
  /**
   * 使用的翻译服务
   * @type {string}
   * @memberof TranslationResultDto
   */
  service_name: string;
}

/**
 * Check if a given object implements the TranslationResultDto interface.
 */
export function instanceOfTranslationResultDto(value: object): value is TranslationResultDto {
  if (!('translated_text' in value) || value.translated_text === undefined) return false;
  if (!('source_language' in value) || value.source_language === undefined) return false;
  if (!('target_language' in value) || value.target_language === undefined) return false;
  if (!('service_name' in value) || value.service_name === undefined) return false;
  return true;
}

export function TranslationResultDtoFromJSON(json: any): TranslationResultDto {
  return TranslationResultDtoFromJSONTyped(json, false);
}

export function TranslationResultDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): TranslationResultDto {
  if (json == null) {
    return json;
  }
  return {
    translated_text: json.translated_text,
    source_language: json.source_language,
    target_language: json.target_language,
    service_name: json.service_name,
  };
}

export function TranslationResultDtoToJSON(json: any): TranslationResultDto {
  return TranslationResultDtoToJSONTyped(json, false);
}

export function TranslationResultDtoToJSONTyped(
  value?: TranslationResultDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    translated_text: value.translated_text,
    source_language: value.source_language,
    target_language: value.target_language,
    service_name: value.service_name,
  };
}
