/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface TranslateDto
 */
export interface TranslateDto {
  /**
   * 要翻译的文本数组
   * @type {Array<string>}
   * @memberof TranslateDto
   */
  texts: Array<string>;
  /**
   * 目标语言代码
   * @type {string}
   * @memberof TranslateDto
   */
  target_lang: string;
  /**
   * 源语言代码（可选，自动检测）
   * @type {string}
   * @memberof TranslateDto
   */
  source_lang?: string;
  /**
   * 指定翻译服务（可选）
   * @type {string}
   * @memberof TranslateDto
   */
  service_name?: string;
}

/**
 * Check if a given object implements the TranslateDto interface.
 */
export function instanceOfTranslateDto(value: object): value is TranslateDto {
  if (!('texts' in value) || value.texts === undefined) return false;
  if (!('target_lang' in value) || value.target_lang === undefined) return false;
  return true;
}

export function TranslateDtoFromJSON(json: any): TranslateDto {
  return TranslateDtoFromJSONTyped(json, false);
}

export function TranslateDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): TranslateDto {
  if (json == null) {
    return json;
  }
  return {
    texts: json.texts,
    target_lang: json.target_lang,
    source_lang: json.source_lang == null ? undefined : json.source_lang,
    service_name: json.service_name == null ? undefined : json.service_name,
  };
}

export function TranslateDtoToJSON(json: any): TranslateDto {
  return TranslateDtoToJSONTyped(json, false);
}

export function TranslateDtoToJSONTyped(
  value?: TranslateDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    texts: value.texts,
    target_lang: value.target_lang,
    source_lang: value.source_lang,
    service_name: value.service_name,
  };
}
