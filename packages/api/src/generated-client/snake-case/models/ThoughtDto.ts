/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ThoughtContentDto } from './ThoughtContentDto';
import {
  ThoughtContentDtoFromJSON,
  ThoughtContentDtoFromJSONTyped,
  ThoughtContentDtoToJSON,
  ThoughtContentDtoToJSONTyped,
} from './ThoughtContentDto';
import type { TitleType } from './TitleType';
import {
  TitleTypeFromJSON,
  TitleTypeFromJSONTyped,
  TitleTypeToJSON,
  TitleTypeToJSONTyped,
} from './TitleType';
import type { Visibility } from './Visibility';
import {
  VisibilityFromJSON,
  VisibilityFromJSONTyped,
  VisibilityToJSON,
  VisibilityToJSONTyped,
} from './Visibility';

/**
 *
 * @export
 * @interface ThoughtDto
 */
export interface ThoughtDto {
  /**
   * 想法ID
   * @type {string}
   * @memberof ThoughtDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof ThoughtDto
   */
  created_at: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof ThoughtDto
   */
  updated_at: Date;
  /**
   * 空间ID
   * @type {string}
   * @memberof ThoughtDto
   */
  space_id: string;
  /**
   * 创建者ID
   * @type {string}
   * @memberof ThoughtDto
   */
  creator_id: string;
  /**
   * 创作板ID
   * @type {string}
   * @memberof ThoughtDto
   */
  board_id?: string;
  /**
   * 标题
   * @type {string}
   * @memberof ThoughtDto
   */
  title: string;
  /**
   * 标题类型
   * @type {TitleType}
   * @memberof ThoughtDto
   */
  title_type: TitleType;
  /**
   * 内容
   * @type {ThoughtContentDto}
   * @memberof ThoughtDto
   */
  content: ThoughtContentDto;
  /**
   * 可见性
   * @type {Visibility}
   * @memberof ThoughtDto
   */
  visibility: Visibility;
  /**
   * 位置信息
   * @type {object}
   * @memberof ThoughtDto
   */
  position: object;
  /**
   * 创作板关联信息（已废弃，请使用 position 字段）
   * @type {object}
   * @memberof ThoughtDto
   * @deprecated
   */
  board_item?: object;
  /**
   * 创作板关联信息（已废弃，请使用 position 字段）
   * @type {Array<string>}
   * @memberof ThoughtDto
   * @deprecated
   */
  board_ids?: Array<string>;
  /**
   * 类型。目前为 swagger 生成 spec 所使用，目的在于区分不同类型的实体，没有实际业务作用
   * @type {string}
   * @memberof ThoughtDto
   */
  type: string;
}

/**
 * Check if a given object implements the ThoughtDto interface.
 */
export function instanceOfThoughtDto(value: object): value is ThoughtDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('space_id' in value) || value.space_id === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('title_type' in value) || value.title_type === undefined) return false;
  if (!('content' in value) || value.content === undefined) return false;
  if (!('visibility' in value) || value.visibility === undefined) return false;
  if (!('position' in value) || value.position === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  return true;
}

export function ThoughtDtoFromJSON(json: any): ThoughtDto {
  return ThoughtDtoFromJSONTyped(json, false);
}

export function ThoughtDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): ThoughtDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    space_id: json.space_id,
    creator_id: json.creator_id,
    board_id: json.board_id == null ? undefined : json.board_id,
    title: json.title,
    title_type: TitleTypeFromJSON(json.title_type),
    content: ThoughtContentDtoFromJSON(json.content),
    visibility: VisibilityFromJSON(json.visibility),
    position: json.position,
    board_item: json.board_item == null ? undefined : json.board_item,
    board_ids: json.board_ids == null ? undefined : json.board_ids,
    type: json.type,
  };
}

export function ThoughtDtoToJSON(json: any): ThoughtDto {
  return ThoughtDtoToJSONTyped(json, false);
}

export function ThoughtDtoToJSONTyped(
  value?: ThoughtDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    space_id: value.space_id,
    creator_id: value.creator_id,
    board_id: value.board_id,
    title: value.title,
    title_type: TitleTypeToJSON(value.title_type),
    content: ThoughtContentDtoToJSON(value.content),
    visibility: VisibilityToJSON(value.visibility),
    position: value.position,
    board_item: value.board_item,
    board_ids: value.board_ids,
    type: value.type,
  };
}
