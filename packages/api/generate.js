#!/usr/bin/env node

const { execSync } = require('node:child_process');
const fs = require('node:fs');
const path = require('node:path');

const OPENAPI_GENERATOR_CLI = 'npx @openapitools/openapi-generator-cli';
const CAMEL_CASE_API_URL = 'http://localhost:4000/api/yaml';
const SNAKE_CASE_API_URL = 'http://localhost:4000/api/yaml/snake_case';
const OUTPUT_DIR = path.join(__dirname, 'src/generated-client');
const TEMP_SPEC_FILE = path.join(__dirname, 'openapi-spec.yaml');

async function generateTypeScriptClient(apiUrl, outputDir) {
  try {
    console.log('🚀 Generating TypeScript client from OpenAPI spec...');

    // Download the OpenAPI spec
    console.log('📥 Downloading OpenAPI spec from:', apiUrl);
    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch OpenAPI spec: ${response.status} ${response.statusText}`);
    }
    const spec = await response.text();

    // Write spec to temporary file
    fs.writeFileSync(TEMP_SPEC_FILE, spec);
    console.log('✅ OpenAPI spec downloaded and saved to temporary file');

    // Always clean up and recreate the output directory
    if (fs.existsSync(outputDir)) {
      fs.rmSync(outputDir, { recursive: true, force: true });
      console.log('🧹 Cleaned up existing src directory');
    }
    fs.mkdirSync(outputDir, { recursive: true });
    console.log('📁 Created fresh src directory');

    // Generate TypeScript client
    console.log('🔧 Generating TypeScript client...');
    let command = `${OPENAPI_GENERATOR_CLI} generate \
      --input-spec ${TEMP_SPEC_FILE} \
      --generator-name typescript-fetch \
      --output ${outputDir} \
      --additional-properties=supportsES6=true,typescriptThreePlus=true,useSingleRequestParameter=false,stringEnums=true,enumPropertyNaming=camelCase,paramNaming=camelCase,withInterfaces=true,legacyDiscriminatorBehavior=false,useTypeScriptRefs=true,disallowAdditionalPropertiesIfNotPresent=false,ensureUniqueParams=true,allowUnicodeIdentifiers=true,prependFormOrBodyParameters=true,enumUnknownDefaultCase=true,usePromise=true,withSeparateModelsAndApi=true,useUnionTypes=true`;
    if (apiUrl === CAMEL_CASE_API_URL) {
      command += ',modelPropertyNaming=camelCase';
    } else {
      command += ',modelPropertyNaming=snake_case';
    }

    execSync(command, { stdio: 'inherit' });
    console.log('✅ TypeScript client generated successfully!');

    // Clean up temporary file
    fs.unlinkSync(TEMP_SPEC_FILE);
    console.log('🧹 Cleaned up temporary spec file');

    // Remove generated runtime.ts since we use a shared one
    const runtimePath = path.join(outputDir, 'runtime.ts');
    if (fs.existsSync(runtimePath)) {
      fs.unlinkSync(runtimePath);
      console.log('🗑️  Removed generated runtime.ts (using shared runtime from src/)');
    }

    // Fix operationId naming by removing controller prefixes
    await fixOperationIdNaming(outputDir);

    // Update API imports to use shared runtime
    await updateRuntimeImports(outputDir);

    // Fix casing issues with acronyms in class names
    await fixAcronymCasing(outputDir);

    // Fix snake_case field naming issues (only for snake-case generation)
    if (apiUrl === SNAKE_CASE_API_URL) {
      await fixSnakeCaseFieldNaming(outputDir);
    }

    // 为了修复鉴别器加的：Fix discriminator import issues。主要处理了引入鉴别器后的 import 和 type 问题
    await fixDiscriminatorImports(outputDir);

    // Append AllApiClients interface and factory to index.ts
    await appendAllApiClientsToIndex(outputDir);
  } catch (error) {
    console.error('❌ Error generating TypeScript client:', error.message);

    // Clean up temporary file if it exists
    if (fs.existsSync(TEMP_SPEC_FILE)) {
      fs.unlinkSync(TEMP_SPEC_FILE);
    }

    process.exit(1);
  }
}

// Fix operationId naming by removing controller prefixes from method names
async function fixOperationIdNaming(outputDir) {
  try {
    console.log('🔧 Fixing operationId naming by removing controller prefixes...');

    const apisDir = path.join(outputDir, 'apis');
    if (!fs.existsSync(apisDir)) {
      console.warn('⚠️  APIs directory not found, skipping operationId fixes');
      return;
    }

    // Mapping for APIs where the controller name doesn't match the filename
    const controllerNameMapping = {
      'ThoughtsApi.ts': 'thought',
      'SnipsApi.ts': 'snip',
      'FavoritesApi.ts': 'favorite',
      'BoardManagementAPIApi.ts': 'board',
      'NoteManagementAPIApi.ts': 'note',
      'ThoughtVersionsApi.ts': 'thoughtVersion',
    };

    // Get all API files
    const apiFiles = fs
      .readdirSync(apisDir)
      .filter((file) => file.endsWith('.ts') && file !== 'index.ts');

    for (const apiFile of apiFiles) {
      const apiFilePath = path.join(apisDir, apiFile);
      let content = fs.readFileSync(apiFilePath, 'utf8');

      // Get the controller name - either from mapping or derived from filename
      let controllerName;
      if (controllerNameMapping[apiFile]) {
        controllerName = controllerNameMapping[apiFile];
      } else {
        // Default: extract API tag name from filename and convert to camelCase
        const apiTagName = apiFile.replace('Api.ts', '');
        controllerName = apiTagName.charAt(0).toLowerCase() + apiTagName.slice(1);
      }

      // Create pattern to match: {controllerName}Controller{Method} -> {method}
      const controllerPattern = new RegExp(
        `\\b${controllerName}Controller([A-Z][a-zA-Z0-9]*)`,
        'g',
      );

      // Replace method names to remove controller prefix
      content = content.replace(controllerPattern, (match, methodPart) => {
        console.log(
          `  ${apiFile}: Replacing ${match} with ${methodPart.charAt(0).toLowerCase() + methodPart.slice(1)}`,
        );
        // Convert first letter to lowercase to follow camelCase convention
        return methodPart.charAt(0).toLowerCase() + methodPart.slice(1);
      });

      fs.writeFileSync(apiFilePath, content);
    }

    console.log('✅ Fixed operationId naming in', apiFiles.length, 'API files');
  } catch (error) {
    console.error('❌ Error fixing operationId naming:', error.message);
  }
}

// Fix casing issues with acronyms in class names (e.g., HTML -> Html)
async function fixAcronymCasing(outputDir) {
  try {
    console.log('🔧 Fixing acronym casing issues...');

    const modelsDir = path.join(outputDir, 'models');
    if (!fs.existsSync(modelsDir)) {
      console.warn('⚠️  Models directory not found, skipping casing fixes');
      return;
    }

    // Map of incorrect casing to correct casing
    const casingMap = {
      HTMLSelection: 'HtmlSelection',
      // Add more mappings as needed
    };

    // Handle OpenAPI generator conflict resolution (files with numeric suffixes)
    const conflictResolutionMap = {
      HtmlSelectionDto0: 'HtmlSelectionDto',
      // Add more mappings as needed
    };

    // Get all model files
    const modelFiles = fs.readdirSync(modelsDir).filter((file) => file.endsWith('.ts'));

    for (const modelFile of modelFiles) {
      const modelFilePath = path.join(modelsDir, modelFile);
      let content = fs.readFileSync(modelFilePath, 'utf8');
      let hasChanges = false;

      // Fix imports and references for each casing issue
      for (const [incorrect, correct] of Object.entries(casingMap)) {
        // Fix import statements (with Dto suffix)
        const importPattern = new RegExp(`from '\\.\\/${incorrect}Dto'`, 'g');
        if (content.match(importPattern)) {
          content = content.replace(importPattern, `from './${correct}Dto'`);
          hasChanges = true;
        }

        // Fix all occurrences of the incorrect casing (including function names)
        // This will fix: HTMLSelectionDto, HTMLSelectionDtoFromJSON, instanceOfHTMLSelectionDto, etc.
        const referencePattern = new RegExp(`\\b${incorrect}`, 'g');
        if (content.match(referencePattern)) {
          content = content.replace(referencePattern, correct);
          hasChanges = true;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(modelFilePath, content);
        console.log(`  Fixed casing in ${modelFile}`);
      }
    }

    // Also rename the actual files if needed and fix their content
    for (const [incorrect, correct] of Object.entries(casingMap)) {
      const incorrectPath = path.join(modelsDir, `${incorrect}Dto.ts`);
      const correctPath = path.join(modelsDir, `${correct}Dto.ts`);

      if (fs.existsSync(incorrectPath)) {
        // Read the file content and fix all occurrences
        let fileContent = fs.readFileSync(incorrectPath, 'utf8');

        // Fix all occurrences of the incorrect casing
        fileContent = fileContent.replace(new RegExp(`\\b${incorrect}`, 'g'), correct);

        // Write the fixed content
        if (fs.existsSync(correctPath)) {
          // If correct file already exists, just update it
          fs.writeFileSync(correctPath, fileContent);
          fs.unlinkSync(incorrectPath);
          console.log(`  Merged ${incorrect}Dto.ts into ${correct}Dto.ts`);
        } else {
          // Write to the correct filename
          fs.writeFileSync(correctPath, fileContent);
          fs.unlinkSync(incorrectPath);
          console.log(`  Renamed ${incorrect}Dto.ts to ${correct}Dto.ts and fixed content`);
        }
      }
    }

    // Handle OpenAPI generator conflict resolution files (with numeric suffixes)
    for (const [conflictFile, correctFile] of Object.entries(conflictResolutionMap)) {
      const conflictPath = path.join(modelsDir, `${conflictFile}.ts`);
      const correctPath = path.join(modelsDir, `${correctFile}.ts`);

      if (fs.existsSync(conflictPath)) {
        // Read the conflict file content (it should already have correct content)
        const fileContent = fs.readFileSync(conflictPath, 'utf8');

        // Write to the correct filename
        fs.writeFileSync(correctPath, fileContent);
        fs.unlinkSync(conflictPath);
        console.log(`  Resolved conflict: renamed ${conflictFile}.ts to ${correctFile}.ts`);

        // Update all other files that import the conflict file
        for (const modelFile of modelFiles) {
          if (modelFile === `${conflictFile}.ts` || modelFile === `${correctFile}.ts`) continue;

          const modelFilePath = path.join(modelsDir, modelFile);
          let content = fs.readFileSync(modelFilePath, 'utf8');
          let hasChanges = false;

          // Fix import statements
          const importPattern = new RegExp(`from '\\.\\/${conflictFile}'`, 'g');
          if (content.match(importPattern)) {
            content = content.replace(importPattern, `from './${correctFile}'`);
            hasChanges = true;
          }

          if (hasChanges) {
            fs.writeFileSync(modelFilePath, content);
            console.log(`  Fixed imports in ${modelFile} (${conflictFile} -> ${correctFile})`);
          }
        }

        // Update index.ts file as well
        const indexPath = path.join(modelsDir, 'index.ts');
        if (fs.existsSync(indexPath)) {
          let indexContent = fs.readFileSync(indexPath, 'utf8');
          const indexImportPattern = new RegExp(`from '\\.\\/${conflictFile}'`, 'g');
          if (indexContent.match(indexImportPattern)) {
            indexContent = indexContent.replace(indexImportPattern, `from './${correctFile}'`);
            fs.writeFileSync(indexPath, indexContent);
            console.log(`  Fixed imports in index.ts (${conflictFile} -> ${correctFile})`);
          }
        }
      }
    }

    // Clean up duplicate exports in index.ts
    const indexPath = path.join(modelsDir, 'index.ts');
    if (fs.existsSync(indexPath)) {
      const indexContent = fs.readFileSync(indexPath, 'utf8');

      // Remove duplicate export lines
      const lines = indexContent.split('\n');
      const uniqueLines = [...new Set(lines)];
      const cleanedContent = uniqueLines.join('\n');

      if (cleanedContent !== indexContent) {
        fs.writeFileSync(indexPath, cleanedContent);
        console.log('  Removed duplicate exports from index.ts');
      }
    }

    console.log('✅ Fixed acronym casing issues');
  } catch (error) {
    console.error('❌ Error fixing acronym casing:', error.message);
  }
}

// Fix discriminator import issues for files using discriminators (GENERIC VERSION)
async function fixDiscriminatorImports(outputDir) {
  try {
    console.log('🔧 Fixing discriminator import issues...');

    const modelsDir = path.join(outputDir, 'models');
    if (!fs.existsSync(modelsDir)) {
      console.warn('⚠️  Models directory not found, skipping discriminator import fixes');
      return;
    }

    // Find all files that contain discriminator patterns
    const modelFiles = fs
      .readdirSync(modelsDir)
      .filter((file) => file.endsWith('.ts') && file !== 'index.ts');
    const discriminatorFiles = [];

    for (const modelFile of modelFiles) {
      const filePath = path.join(modelsDir, modelFile);
      const content = fs.readFileSync(filePath, 'utf8');

      // Check if this file contains complex discriminator types (intersection types with discriminator property)
      // Match patterns like ({ type: or ({ $class: or other discriminator properties
      if (
        content.match(/\(\s*{\s*\$?\w+:\s*'/g) &&
        content.includes('} &') &&
        content.includes('export type')
      ) {
        discriminatorFiles.push(modelFile);
        console.log(`  Found discriminator pattern in ${modelFile}`);
      }
    }

    if (discriminatorFiles.length === 0) {
      console.log('  No discriminator files found, skipping');
      return;
    }

    console.log(
      `  Processing ${discriminatorFiles.length} discriminator files: ${discriminatorFiles.join(', ')}`,
    );

    for (const fileName of discriminatorFiles) {
      const filePath = path.join(modelsDir, fileName);
      let content = fs.readFileSync(filePath, 'utf8');

      console.log(`  Processing ${fileName}...`);

      // Extract the type name from the file
      const typeMatch = content.match(/export type (\w+) =/);
      if (!typeMatch) {
        console.warn(`  ${fileName}: Could not find type definition, skipping`);
        continue;
      }

      const typeName = typeMatch[1];
      console.log(`  Found type: ${typeName}`);

      // Extract all referenced DTO types from the union type definition
      const unionTypeMatch = content.match(/export type \w+ =[\s\S]*?;/);
      if (!unionTypeMatch) {
        console.warn(`  ${fileName}: Could not find union type definition, skipping`);
        continue;
      }

      const unionTypeContent = unionTypeMatch[0];
      console.log(`  Union type content preview: ${unionTypeContent.substring(0, 200)}...`);

      // Extract DTO names from patterns like "} & SomeDto)" or "} & SomeDto" - improved regex
      const dtoMatches = unionTypeContent.match(/}\s*&\s*\w+Dto/g);
      if (!dtoMatches || dtoMatches.length === 0) {
        console.warn(`  ${fileName}: Could not find DTO types in union, skipping`);
        console.log(`  Union type content: ${unionTypeContent}`);
        continue;
      }

      console.log(`  Raw DTO matches: ${dtoMatches.join(', ')}`);

      // Extract unique DTO names
      const dtoTypes = [
        ...new Set(
          dtoMatches
            .map((match) => {
              const result = match.match(/}\s*&\s*(\w+Dto)/);
              return result ? result[1] : null;
            })
            .filter(Boolean),
        ),
      ];

      if (dtoTypes.length === 0) {
        console.warn(`  ${fileName}: No DTO types extracted, skipping`);
        continue;
      }

      console.log(`  ${fileName}: Extracted DTO types: ${dtoTypes.join(', ')}`);

      // Generate import statements
      const importStatements = [];
      for (const dtoName of dtoTypes) {
        importStatements.push(`import type { ${dtoName} } from './${dtoName}';`);
        importStatements.push(`import {`);
        importStatements.push(`  ${dtoName}FromJSON,`);
        importStatements.push(`  ${dtoName}FromJSONTyped,`);
        importStatements.push(`  ${dtoName}ToJSON,`);
        importStatements.push(`  instanceOf${dtoName},`);
        importStatements.push(`} from './${dtoName}';`);
      }

      // Find the position after the header comment to insert imports
      // Look for the end of the main header comment block (after the OpenAPI Generator comment)
      const headerEndPattern = /\*\s*Do not edit the class manually\.\s*\*\/\s*\n/;
      const headerMatch = content.match(headerEndPattern);

      if (headerMatch) {
        const insertPosition = headerMatch.index + headerMatch[0].length;
        const beforeImports = content.substring(0, insertPosition);
        const afterImports = content.substring(insertPosition);

        const importsBlock = `\n${importStatements.join('\n')}\n`;
        content = beforeImports + importsBlock + afterImports;

        // Replace complex discriminator implementation with simple union type

        // Create simple union type
        const simpleUnionType = `export type ${typeName} =
${dtoTypes.map((dto) => `  | ${dto}`).join('\n')};`;

        // Replace the complex discriminator type definition
        content = content.replace(/export type \w+ =[\s\S]*?;/, simpleUnionType);

        // Extract discriminator mapping and propertyName from original content
        const typeMapping = new Map();
        const discriminatorProperty = '$class'; // 直接使用 $class

        // Parse the original switch statements to extract type mappings
        const switchMatches = content.match(/case '([^']+)':\s*return[^;]+(\w+Dto)FromJSONTyped/g);
        if (switchMatches) {
          for (const match of switchMatches) {
            const typeMatch = match.match(/case '([^']+)':/);
            const dtoMatch = match.match(/(\w+Dto)FromJSONTyped/);
            if (typeMatch && dtoMatch) {
              typeMapping.set(typeMatch[1], dtoMatch[1]);
            }
          }
        }

        // Generate FromJSONTyped function with discriminator logic
        const fromJSONFuncName = `${typeName}FromJSONTyped`;
        let fromJSONCases = '';

        if (typeMapping.size > 0) {
          // Use discriminator type mapping
          fromJSONCases = Array.from(typeMapping.entries())
            .map(
              ([typeValue, dtoName]) =>
                `    case '${typeValue}':
      return ${dtoName}FromJSONTyped(json, true);`,
            )
            .join('\n');
        } else {
          // Fallback: if no mapping, create mapping based on DTO class names
          for (const dto of dtoTypes) {
            typeMapping.set(dto, dto);
          }
          fromJSONCases = dtoTypes
            .map(
              (dto) =>
                `    case '${dto}':
      return ${dto}FromJSONTyped(json, true);`,
            )
            .join('\n');
        }

        const simpleFromJSONTyped = `export function ${fromJSONFuncName}(
  json: any,
  _ignoreDiscriminator: boolean,
): ${typeName} {
  if (json == null) {
    return json;
  }

  switch (json.${discriminatorProperty}) {
${fromJSONCases}
    default:
      return json;
  }
}`;

        // Generate ToJSONTyped function - first try discriminator, then fall back to instanceOf
        const toJSONFuncName = `${typeName}ToJSONTyped`;

        // Generate switch cases for discriminator
        const switchCases = Array.from(typeMapping.entries())
          .map(
            ([typeValue, dtoName]) =>
              `    case '${typeValue}':
      return ${dtoName}ToJSON(value);`,
          )
          .join('\n');

        // Generate instanceOf checks as fallback
        const instanceOfCases = dtoTypes
          .map(
            (dto) =>
              `  if (instanceOf${dto}(value)) {
    return ${dto}ToJSON(value);
  }`,
          )
          .join('\n');

        const simpleToJSONTyped = `export function ${toJSONFuncName}(
  value?: any,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  // First try to use discriminator property if it exists
  if (value.${discriminatorProperty}) {
    switch (value.${discriminatorProperty}) {
${switchCases}
      default:
        // Fall through to instanceOf checks
        break;
    }
  }

  // Fallback to instanceOf checks
${instanceOfCases}

  return {};
}`;

        // Replace the function implementations with more precise regex
        const fromJSONRegex = new RegExp(
          `export function ${fromJSONFuncName}\\([\\s\\S]*?\\n}`,
          's',
        );
        const toJSONRegex = new RegExp(`export function ${toJSONFuncName}\\([\\s\\S]*?\\n}`, 's');

        content = content.replace(fromJSONRegex, simpleFromJSONTyped);
        content = content.replace(toJSONRegex, simpleToJSONTyped);

        fs.writeFileSync(filePath, content);
        console.log(`  ✅ Fixed discriminator imports and code issues in ${fileName}`);
      } else {
        console.warn(`  Could not find header end in ${fileName}, skipping import fix`);
      }
    }

    console.log('✅ Fixed discriminator import issues');
  } catch (error) {
    console.error('❌ Error fixing discriminator imports:', error.message);
  }
}
// Fix snake_case field naming issues for specific fields
async function fixSnakeCaseFieldNaming(outputDir) {
  try {
    console.log('🔧 Fixing snake_case field naming issues...');

    const modelsDir = path.join(outputDir, 'models');
    if (!fs.existsSync(modelsDir)) {
      console.warn('⚠️  Models directory not found, skipping snake_case field fixes');
      return;
    }

    // Map of incorrect snake_case naming to correct snake_case naming
    const fieldNamingMap = {
      ai2nd_response_language: 'ai_2nd_response_language',
      // Add more mappings as needed in the future
    };

    // Get all model files
    const modelFiles = fs.readdirSync(modelsDir).filter((file) => file.endsWith('.ts'));

    let totalFixesApplied = 0;

    for (const modelFile of modelFiles) {
      const modelFilePath = path.join(modelsDir, modelFile);
      let content = fs.readFileSync(modelFilePath, 'utf8');
      let hasChanges = false;

      // Fix field names in all contexts
      for (const [incorrect, correct] of Object.entries(fieldNamingMap)) {
        // Create a global pattern to match the incorrect field name in any context
        const globalPattern = new RegExp(`\\b${incorrect}\\b`, 'g');

        if (content.match(globalPattern)) {
          content = content.replace(globalPattern, correct);
          hasChanges = true;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(modelFilePath, content);
        console.log(`  Fixed snake_case field naming in ${modelFile}`);
        totalFixesApplied++;
      }
    }

    console.log(`✅ Fixed snake_case field naming issues in ${totalFixesApplied} files`);
  } catch (error) {
    console.error('❌ Error fixing snake_case field naming:', error.message);
  }
}

// Update API imports to use shared runtime instead of local runtime.ts
async function updateRuntimeImports(outputDir) {
  try {
    console.log('🔧 Updating runtime imports...');

    let totalFilesUpdated = 0;

    // Update API files
    const apisDir = path.join(outputDir, 'apis');
    if (fs.existsSync(apisDir)) {
      const apiFiles = fs
        .readdirSync(apisDir)
        .filter((file) => file.endsWith('.ts') && file !== 'index.ts');

      for (const apiFile of apiFiles) {
        const apiFilePath = path.join(apisDir, apiFile);
        let content = fs.readFileSync(apiFilePath, 'utf8');

        // Replace import from '../runtime' to '../../../runtime'
        content = content.replace(
          /import \* as runtime from '\.\.\/runtime';/g,
          "import * as runtime from '../../../runtime';",
        );

        fs.writeFileSync(apiFilePath, content);
      }
      totalFilesUpdated += apiFiles.length;
    }

    // Update model files
    const modelsDir = path.join(outputDir, 'models');
    if (fs.existsSync(modelsDir)) {
      const modelFiles = fs
        .readdirSync(modelsDir)
        .filter((file) => file.endsWith('.ts') && file !== 'index.ts');

      for (const modelFile of modelFiles) {
        const modelFilePath = path.join(modelsDir, modelFile);
        let content = fs.readFileSync(modelFilePath, 'utf8');

        // Replace import from '../runtime' to '../../../runtime'
        content = content.replace(/import \{ [^}]+ \} from '\.\.\/runtime';/g, (match) => {
          return match.replace("'../runtime'", "'../../../runtime'");
        });

        fs.writeFileSync(modelFilePath, content);
      }
      totalFilesUpdated += modelFiles.length;
    }

    const indexFile = path.join(outputDir, 'index.ts');
    let indexContent = fs.readFileSync(indexFile, 'utf8');
    indexContent = indexContent.replace(/export \* from '\.\/runtime';/g, '');
    fs.writeFileSync(indexFile, indexContent);

    console.log('✅ Updated runtime imports in', totalFilesUpdated, 'files');
  } catch (error) {
    console.error('❌ Error updating runtime imports:', error.message);
  }
}

// Append AllApiClients interface and factory to the main index.ts file
async function appendAllApiClientsToIndex(outputDir) {
  try {
    console.log('📝 Appending AllApiClients interface to index.ts...');

    const mainIndexPath = path.join(outputDir, 'index.ts');
    const apisIndexPath = path.join(outputDir, 'apis', 'index.ts');

    if (!fs.existsSync(mainIndexPath) || !fs.existsSync(apisIndexPath)) {
      console.warn('⚠️  Index files not found, skipping AllApiClients generation');
      return;
    }

    // Read the APIs index file to get all exported API classes
    const apisIndexContent = fs.readFileSync(apisIndexPath, 'utf8');

    // Extract API class names from export statements
    const apiExports = [];
    const exportMatches = apisIndexContent.match(/export \* from '\.\/([^']+)';/g);

    if (exportMatches) {
      for (const match of exportMatches) {
        const apiName = match.match(/export \* from '\.\/([^']+)';/)[1];
        apiExports.push(apiName);
      }
    }

    // Generate the AllApiClients interface and factory function
    const allApiClientsContent = `
// This file is generated by generate.js and provides a unified entry point for all API classes
// DO NOT MODIFY DIRECTLY
import { Configuration } from '../../runtime';
${apiExports.map((apiName) => `import { ${apiName} } from './apis/${apiName}';`).join('\n')}

export interface AllApiClients {
${apiExports.map((apiName) => `  ${apiName.charAt(0).toLowerCase() + apiName.slice(1)}: ${apiName};`).join('\n')}
}

export function createAllApiClients(configuration: Configuration): AllApiClients {
  return {
${apiExports.map((apiName) => `    ${apiName.charAt(0).toLowerCase() + apiName.slice(1)}: new ${apiName}(configuration),`).join('\n')}
  };
}
`;

    // Read the current main index.ts content
    let mainIndexContent = fs.readFileSync(mainIndexPath, 'utf8');

    // Remove any existing AllApiClients content (for regeneration)
    mainIndexContent = mainIndexContent.replace(
      /\n\n\/\/ AllApiClients interface and factory - AUTO-GENERATED[\s\S]*$/,
      '',
    );

    // Append the new AllApiClients content
    mainIndexContent += allApiClientsContent;

    // Write back to the main index.ts file
    fs.writeFileSync(mainIndexPath, mainIndexContent);

    console.log(
      '✅ Appended AllApiClients interface to index.ts with API classes:',
      apiExports.join(', '),
    );
  } catch (error) {
    console.error('❌ Error appending AllApiClients to index.ts:', error.message);
  }
}

// Check if API server is running
async function checkApiServer() {
  try {
    const response = await fetch(CAMEL_CASE_API_URL);
    if (!response.ok) {
      throw new Error(`API server not responding: ${response.status}`);
    }
    console.log('✅ API server is running and accessible');
    return true;
  } catch (error) {
    console.error('❌ API server is not accessible:', error.message);
    console.log('\n💡 Make sure the API server is running on http://localhost:4000');
    console.log('   Run: cd apps/youapi && npm run start:dev');
    return false;
  }
}

// Check if Java is available
function checkJava() {
  try {
    execSync('java -version', { stdio: 'ignore' });
    console.log('✅ Java is available');
    return true;
  } catch (_error) {
    console.error('❌ Java is not available. Please install Java to continue.');
    console.log('💡 Install Java from: https://adoptium.net/ or use your system package manager');
    return false;
  }
}

// Main execution
async function main() {
  console.log('🔍 Checking requirements...');

  const javaAvailable = checkJava();
  if (!javaAvailable) {
    process.exit(1);
  }

  console.log('🔍 Checking API server availability...');
  const serverRunning = await checkApiServer();

  if (!serverRunning) {
    process.exit(1);
  }

  await generateTypeScriptClient(CAMEL_CASE_API_URL, path.join(OUTPUT_DIR, 'camel-case'));
  await generateTypeScriptClient(SNAKE_CASE_API_URL, path.join(OUTPUT_DIR, 'snake-case'));

  // Run biome to auto-fix and format the generated code
  try {
    console.log('🧹 Running biome check --write on generated code...');
    execSync('npm run lint:generated', { stdio: 'inherit' });
    console.log('✅ Biome formatting and auto-fix complete!');
  } catch (e) {
    console.warn('⚠️  Biome formatting failed:', e.message);
  }

  // Re-apply discriminator fixes after biome formatting (biome may revert our changes)
  console.log('🔧 Re-applying discriminator fixes after biome formatting...');
  await fixDiscriminatorImports(path.join(OUTPUT_DIR, 'camel-case'));
  await fixDiscriminatorImports(path.join(OUTPUT_DIR, 'snake-case'));

  console.log('\n🎉 TypeScript client generation completed!');
  console.log(`📁 Generated client is available in: ${OUTPUT_DIR}`);
  console.log('\n📋 Next steps:');
  console.log(`1. Review the generated code in ${OUTPUT_DIR}`);
  console.log('2. Import and use the client in your application');
  console.log('3. The client is ready for browser usage without Node.js dependencies');
}

main().catch(console.error);
