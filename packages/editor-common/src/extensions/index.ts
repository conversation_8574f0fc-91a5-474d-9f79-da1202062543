import { getSchema } from '@tiptap/core';
import { Document } from '@tiptap/extension-document';
import { Text } from '@tiptap/extension-text';
import { Audio } from './audio';
import { Blockquote } from './blockquote';
import { BulletList } from './bullet-list';
import { CodeBlock } from './code-block';
import { DiffBlock } from './diff-block';
import { DiffChange } from './diff-change';
import { Divider } from './divider';
import { HardBreak } from './hardbreak';
import { Heading } from './heading';
import { Image } from './image';
import { Link } from './link';
import { ListItem } from './list-item';
import { Bold, Code, Highlight, Italic, Strike, Underline } from './mark';
import { Mathematics } from './mathematics';
import { Mermaid } from './mermaid';
import { OrderedList } from './ordered-list';
import { Paragraph } from './paragraph';
import { SVG } from './svg';
import { Table } from './table';
import { TableCell } from './table-cell';
import { TableHeader } from './table-header';
import { TableRow } from './table-row';
import { TaskItem } from './task-item';
import { TaskList } from './task-list';
import { SelectionEnd, SelectionStart } from './user-selection';
import { Youtube } from './youtube';
export { generateJSON } from '@tiptap/core';

export const SchemaExtension = [
  // node
  HardBreak,
  BulletList,
  Blockquote,
  Heading,
  CodeBlock,
  Mermaid,
  DiffBlock,
  Youtube,
  Paragraph,
  Table,
  TableCell,
  TableHeader,
  TableRow,
  TaskItem,
  TaskList,
  Divider,
  Image,
  ListItem,
  OrderedList,
  Mathematics,
  Document,
  Text,
  SelectionStart,
  SelectionEnd,
  // mark
  DiffChange,
  Code,
  Link,
  Bold,
  Italic,
  Strike,
  Underline,
  SVG,
  Audio,
  Highlight,
];

export const EditorSchema = getSchema(SchemaExtension);

export {
  // node
  SelectionStart,
  SelectionEnd,
  HardBreak,
  BulletList,
  Blockquote,
  Heading,
  CodeBlock,
  Mermaid,
  DiffBlock,
  Youtube,
  Paragraph,
  Table,
  TableCell,
  TableHeader,
  TableRow,
  TaskItem,
  TaskList,
  Divider,
  Image,
  ListItem,
  OrderedList,
  Mathematics,
  Document,
  Text,
  SVG,
  // mark
  DiffChange,
  Code,
  Link,
  Bold,
  Italic,
  Strike,
  Underline,
  Audio,
  Highlight,
};

export * from '@tiptap/extension-document';
export * from '@tiptap/extension-text';
export * from './audio';
export * from './blockquote';
export * from './bullet-list';
export * from './code-block';
export * from './diff-block';
export * from './diff-change';
export * from './divider';
export * from './hardbreak';
export * from './heading';
export * from './image';
export * from './link';
export * from './list-item';
export * from './mark';
export * from './mathematics';
export * from './mermaid';
export * from './mermaid';
export * from './ordered-list';
export * from './paragraph';
export * from './svg';
export * from './table';
export * from './table-cell';
export * from './table-header';
export * from './table-row';
export * from './task-item';
export * from './task-list';
export * from './user-selection';
export * from './youtube';
